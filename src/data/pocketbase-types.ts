/**
* This file was @generated using pocketbase-typegen
*/

import type PocketBase from 'pocketbase'
import type { RecordService } from 'pocketbase'

export enum Collections {
	Authorigins = "_authOrigins",
	Externalauths = "_externalAuths",
	Mfas = "_mfas",
	Otps = "_otps",
	Superusers = "_superusers",
	ActivityLogs = "activity_logs",
	Barristers = "barristers",
	ClaimFundings = "claim_fundings",
	Claims = "claims",
	CoFunders = "co_funders",
	EducationalContent = "educational_content",
	Enquiries = "enquiries",
	ErrorLogs = "error_logs",
	ExpertWitnesses = "expert_witnesses",
	KnowledgeTests = "knowledge_tests",
	Notifications = "notifications",
	Payments = "payments",
	SecurityLogs = "security_logs",
	Solicitors = "solicitors",
	TestAttempts = "test_attempts",
	UserProfiles = "user_profiles",
	Users = "users",
}

// Alias types for improved usability
export type IsoDateString = string
export type RecordIdString = string
export type HTMLString = string

// System fields
export type BaseSystemFields<T = never> = {
	id: RecordIdString
	collectionId: string
	collectionName: Collections
	expand?: T
}

export type AuthSystemFields<T = never> = {
	email: string
	emailVisibility: boolean
	username: string
	verified: boolean
} & BaseSystemFields<T>

// Record types for each collection

export type AuthoriginsRecord = {
	collectionRef: string
	created?: IsoDateString
	fingerprint: string
	id: string
	recordRef: string
	updated?: IsoDateString
}

export type ExternalauthsRecord = {
	collectionRef: string
	created?: IsoDateString
	id: string
	provider: string
	providerId: string
	recordRef: string
	updated?: IsoDateString
}

export type MfasRecord = {
	collectionRef: string
	created?: IsoDateString
	id: string
	method: string
	recordRef: string
	updated?: IsoDateString
}

export type OtpsRecord = {
	collectionRef: string
	created?: IsoDateString
	id: string
	password: string
	recordRef: string
	sentTo?: string
	updated?: IsoDateString
}

export type SuperusersRecord = {
	created?: IsoDateString
	email: string
	emailVisibility?: boolean
	id: string
	password: string
	tokenKey: string
	updated?: IsoDateString
	verified?: boolean
}

export type ActivityLogsRecord<Tdata = unknown> = {
	action?: string
	created?: IsoDateString
	data?: null | Tdata
	id: string
	ip_address?: string
	updated?: IsoDateString
	user_id?: RecordIdString
}

export type BarristersRecord = {
	barrister_with_conduct?: string
	chambers?: string
	claims?: RecordIdString
	created?: IsoDateString
	email?: string
	id: string
	updated?: IsoDateString
}

export enum ClaimFundingsStatusOptions {
	"pending" = "pending",
	"approved" = "approved",
	"rejected" = "rejected",
	"completed" = "completed",
}

export enum ClaimFundingsFundingOptionOptions {
	"discretionary" = "discretionary",
	"non-discretionary" = "non-discretionary",
}
export type ClaimFundingsRecord = {
	amount?: number
	claim_id?: RecordIdString[]
	co_funder_id?: RecordIdString
	continue_declaration_signature?: string
	created?: IsoDateString
	duration?: number
	expected_return?: number
	funding_date?: IsoDateString
	funding_option?: ClaimFundingsFundingOptionOptions
	id: string
	lenders_app_signature?: string
	non_recourse_agreement_signature?: string
	payment_reference?: string
	percentage?: number
	status?: ClaimFundingsStatusOptions
	updated?: IsoDateString
}

export enum ClaimsStatusOptions {
	"STAGE 1: Pre-Action" = "STAGE 1: Pre-Action",
	"STAGE 2: Letter Before Action" = "STAGE 2: Letter Before Action",
	"STAGE 3: Claim Issued and Served" = "STAGE 3: Claim Issued and Served",
	"STAGE 4: Particulars of Claim Served" = "STAGE 4: Particulars of Claim Served",
	"STAGE 5: Defence Received" = "STAGE 5: Defence Received",
	"STAGE 6: Case Management Court Hearing" = "STAGE 6: Case Management Court Hearing",
	"STAGE 7: Directions Hearings (Court)" = "STAGE 7: Directions Hearings (Court)",
	"STAGE 8: Applications Hearings" = "STAGE 8: Applications Hearings",
	"STAGE 9: Witness Statements" = "STAGE 9: Witness Statements",
	"STAGE 10: Expert Reports" = "STAGE 10: Expert Reports",
	"STAGE 11: Disclosure - Evidence" = "STAGE 11: Disclosure - Evidence",
	"STAGE 12: Inspections" = "STAGE 12: Inspections",
	"STAGE 13: Pre-Trial Review" = "STAGE 13: Pre-Trial Review",
	"STAGE 14: Trial Preparations" = "STAGE 14: Trial Preparations",
	"STAGE 15: Part 36/Offers/Mediation" = "STAGE 15: Part 36/Offers/Mediation",
}

export enum ClaimsApprovalStatusOptions {
	"Pending Review" = "Pending Review",
	"Approved" = "Approved",
	"Rejected" = "Rejected",
	"Matured" = "Matured",
}
export type ClaimsRecord<Taudit_trail = unknown> = {
	approval_status?: ClaimsApprovalStatusOptions
	audit_trail?: null | Taudit_trail
	claim_value?: number
	cost_schedule?: string
	created?: IsoDateString
	duration?: number
	fixed_rate_funder_return?: number
	funding_required?: string
	funding_target?: number
	id: string
	legal_opinion?: string
	risk_committee_approval?: string
	solicitors?: RecordIdString[]
	status?: ClaimsStatusOptions
	success_prospects?: number
	supporting_documents?: string[]
	total_funding_received?: number
	updated?: IsoDateString
	users?: RecordIdString[]
}

export enum CoFundersRiskAppetiteOptions {
	"low" = "low",
	"medium" = "medium",
	"high" = "high",
}

export enum CoFundersKycStatusOptions {
	"pending" = "pending",
	"approved" = "approved",
	"rejected" = "rejected",
}

export enum CoFundersLevelOptions {
	"E0" = "0",
	"E1" = "1",
	"E2" = "2",
	"E3" = "3",
	"E4" = "4",
}

export enum CoFundersIdTypeOptions {
	"passport" = "passport",
	"national_id" = "national_id",
	"drivers_license" = "drivers_license",
}
export type CoFundersRecord<Tassets_portfolio = unknown, Tbank_account_details = unknown, Tlevel_test_scores = unknown> = {
	articles_read?: RecordIdString[]
	assets_portfolio?: null | Tassets_portfolio
	associate?: boolean
	associate_tc_agreed?: boolean
	available_surplus_cash?: string
	bank_account_details?: null | Tbank_account_details
	city?: string
	claims?: RecordIdString[]
	country_of_residence?: string
	date_of_birth?: IsoDateString
	date_signed_level3_tc?: IsoDateString
	date_signed_nda?: IsoDateString
	id: string
	id_type?: CoFundersIdTypeOptions
	identification_document?: string[]
	kyc_status?: CoFundersKycStatusOptions
	l3_agreement_signature?: string
	level1_approved?: boolean
	level1_requested?: boolean
	level2_approved?: boolean
	level2_requested?: boolean
	level?: CoFundersLevelOptions
	level_test_scores?: null | Tlevel_test_scores
	mobile?: string
	nationality?: string
	nda_signature?: string
	net_worth?: string
	occupation?: string
	postcode?: string
	proof_of_residence?: string[]
	residential_address?: string
	risk_appetite?: CoFundersRiskAppetiteOptions
	signed_nda?: boolean
	state_province?: string
	test_passed?: boolean
	user_id?: RecordIdString
}

export enum EducationalContentContentTypeOptions {
	"blog" = "blog",
	"podcast" = "podcast",
	"newsletter" = "newsletter",
	"course" = "course",
	"article" = "article",
}

export enum EducationalContentAccessLevelOptions {
	"E0" = "0",
	"E1" = "1",
	"E2" = "2",
	"E3" = "3",
	"E4" = "4",
}
export type EducationalContentRecord<Tcontent = unknown> = {
	access_level?: EducationalContentAccessLevelOptions[]
	author_id?: RecordIdString
	completion_rate?: number
	content?: null | Tcontent
	content_type?: EducationalContentContentTypeOptions
	id: string
	media_files?: string[]
	published_at?: IsoDateString
	title?: string
	views_count?: number
}

export enum EnquiriesStatusOptions {
	"new" = "new",
	"in_progress" = "in_progress",
	"resolved" = "resolved",
	"closed" = "closed",
}
export type EnquiriesRecord = {
	created?: IsoDateString
	email: string
	id: string
	ip_address?: string
	message: string
	name: string
	source?: string
	status: EnquiriesStatusOptions
	subject: string
	updated?: IsoDateString
	user_agent?: string
}

export type ErrorLogsRecord<Tdata = unknown> = {
	action?: string
	created?: IsoDateString
	data?: null | Tdata
	id: string
	ip_address?: string
	type?: string
	updated?: IsoDateString
	user_id?: RecordIdString
}

export enum ExpertWitnessesTypeOptions {
	"legal" = "legal",
	"financial" = "financial",
	"engineer" = "engineer",
	"construction" = "construction",
	"other" = "other",
}
export type ExpertWitnessesRecord = {
	claims?: RecordIdString[]
	created?: IsoDateString
	email?: string
	firm_name?: string
	id: string
	name?: string
	profession?: string
	type?: ExpertWitnessesTypeOptions
	updated?: IsoDateString
}

export enum KnowledgeTestsPossibleAnswersOptions {
	"Claimants" = "Claimants",
	"Retailers" = "Retailers",
	"Judges" = "Judges",
	"To photocopy case documents" = "To photocopy case documents",
	"To intimidate the claimant" = "To intimidate the claimant",
	"To file paperwork" = "To file paperwork",
	"The opposition" = "The opposition",
	"Barristers" = "Barristers",
	"The jury" = "The jury",
	"Arranging meetings" = "Arranging meetings",
	"Providing reports and testimony" = "Providing reports and testimony",
	"Catering" = "Catering",
	"Deny the claim" = "Deny the claim",
	"Offer customer rewards" = "Offer customer rewards",
	"Find a new client" = "Find a new client",
	"Regulated professionals" = "Regulated professionals",
	"Friends and family" = "Friends and family",
	"Anyone" = "Anyone",
	"Natural disasters" = "Natural disasters",
	"Financial losses in litigation" = "Financial losses in litigation",
	"Market fluctuations" = "Market fluctuations",
	"Provides financial funding for strong claims" = "Provides financial funding for strong claims",
	"Funds claims without any due diligence" = "Funds claims without any due diligence",
	"Gives general legal advice" = "Gives general legal advice",
	"Businesses seeking investment" = "Businesses seeking investment",
	"People with strong cases against professionals" = "People with strong cases against professionals",
	"Anyone looking for legal advice" = "Anyone looking for legal advice",
	"Doing nothing" = "Doing nothing",
	"Sending the claimant gifts" = "Sending the claimant gifts",
	"Overwhelming the claimant" = "Overwhelming the claimant",
	"To write letters" = "To write letters",
	"To review the claim and give advice" = "To review the claim and give advice",
	"To host social events" = "To host social events",
	"To make less money" = "To make less money",
	"To keep getting hired" = "To keep getting hired",
	"To win a popularity contest" = "To win a popularity contest",
	"To avoid high costs and reputational damage" = "To avoid high costs and reputational damage",
	"To hold a press conference" = "To hold a press conference",
	"To spend more money" = "To spend more money",
	"Proposals for collaborative projects" = "Proposals for collaborative projects",
	"Proposals to settle the claim" = "Proposals to settle the claim",
	"Offers to dine out" = "Offers to dine out",
	"Taking on any claims" = "Taking on any claims",
	"Sending out thank you cards to clients" = "Sending out thank you cards to clients",
	"Careful due-diligence of each claim" = "Careful due-diligence of each claim",
	"They vote on it" = "They vote on it",
	"They check if the case is strong and likely to win" = "They check if the case is strong and likely to win",
	"They choose based on the case name" = "They choose based on the case name",
	"They send the defendant a gift" = "They send the defendant a gift",
	"They get ready for a possible counterattack" = "They get ready for a possible counterattack",
	"They write a pleading letter to the judge" = "They write a pleading letter to the judge",
	"You get a new lawyer" = "You get a new lawyer",
	"You have to pay the defendant’s costs" = "You have to pay the defendant’s costs",
	"You win the case automatically" = "You win the case automatically",
	"They get paid last" = "They get paid last",
	"They are repaid first" = "They are repaid first",
	"They do not get paid at all" = "They do not get paid at all",
	"Only the claimant" = "Only the claimant",
	"The general public" = "The general public",
	"Lawyers and barristers and insurers" = "Lawyers and barristers and insurers",
	"General advice" = "General advice",
	"Advanced confidential information" = "Advanced confidential information",
	"World news" = "World news",
	"Nothing" = "Nothing",
	"Sign a Non-Disclosure Agreement (NDA)" = "Sign a Non-Disclosure Agreement (NDA)",
	"Bypass the other levels" = "Bypass the other levels",
	"Before the claim has started" = "Before the claim has started",
	"At different points during the claim" = "At different points during the claim",
	"Only after the claim is finished" = "Only after the claim is finished",
}
export type KnowledgeTestsRecord = {
	answer?: string
	id: string
	possible_answers?: KnowledgeTestsPossibleAnswersOptions[]
	question?: string
}

export enum NotificationsTypeOptions {
	"system" = "system",
	"claim" = "claim",
	"education" = "education",
	"security" = "security",
	"funding" = "funding",
}
export type NotificationsRecord<Tdata = unknown> = {
	created?: IsoDateString
	data?: null | Tdata
	id: string
	message?: string
	read?: boolean
	title?: string
	type?: NotificationsTypeOptions
	updated?: IsoDateString
	user_id?: RecordIdString
}

export enum PaymentsPaymentStatusOptions {
	"pending" = "pending",
	"success" = "success",
	"failed" = "failed",
}
export type PaymentsRecord = {
	amount_paid?: string
	created?: IsoDateString
	id: string
	payment_method?: string
	payment_status?: PaymentsPaymentStatusOptions
	receipt_url?: string
	stripe_payment_id?: string
	subscriber?: RecordIdString
	updated?: IsoDateString
	user?: RecordIdString
}

export enum SecurityLogsSeverityOptions {
	"low" = "low",
	"medium" = "medium",
	"high" = "high",
}
export type SecurityLogsRecord<Tdata = unknown> = {
	created?: IsoDateString
	data?: null | Tdata
	event?: string
	id: string
	severity?: SecurityLogsSeverityOptions
	updated?: IsoDateString
	user_id?: RecordIdString
}

export enum SolicitorsVerificationStatusOptions {
	"pending" = "pending",
	"approved" = "approved",
	"rejected" = "rejected",
}
export type SolicitorsRecord = {
	agree_solicitor_terms?: boolean
	agree_solicitor_terms_date?: IsoDateString
	claims?: RecordIdString[]
	contact_number: string
	firm_address: string
	id: string
	law_firm_name: string
	legal_practitioner_certificate?: string
	signature?: string
	solicitor_name: string
	solicitor_position: string
	sra_number: string
	user_id: RecordIdString
	verification_status: SolicitorsVerificationStatusOptions
}

export type TestAttemptsRecord = {
	attempt_date?: IsoDateString
	cofunder_id?: RecordIdString
	id: string
	level?: number
	passed?: boolean
	score?: number
	user_id?: RecordIdString
}

export type UserProfilesRecord = {
	created?: IsoDateString
	id: string
	updated?: IsoDateString
}

export enum UsersUserTypeOptions {
	"solicitor" = "solicitor",
	"co-funder" = "co-funder",
	"admin" = "admin",
	"claimant" = "claimant",
}

export enum UsersLevelOptions {
	"E0" = "0",
	"E1" = "1",
	"E2" = "2",
	"E3" = "3",
	"E4" = "4",
}
export type UsersRecord = {
	address_line1?: string
	address_line2?: string
	avatar?: string
	city?: string
	country_code?: string
	created?: IsoDateString
	email: string
	emailVisibility?: boolean
	first_name?: string
	id: string
	is_permitted_user?: boolean
	last_name?: string
	law_firm_name?: string
	level?: UsersLevelOptions
	mobile?: string
	name?: string
	opt_out?: boolean
	opt_out_date?: IsoDateString
	password: string
	position?: string
	postcode?: string
	signature?: string
	sra_number?: string
	state_province?: string
	tokenKey: string
	updated?: IsoDateString
	user_type?: UsersUserTypeOptions
	verified?: boolean
}

// Response types include system fields and match responses from the PocketBase API
export type AuthoriginsResponse<Texpand = unknown> = Required<AuthoriginsRecord> & BaseSystemFields<Texpand>
export type ExternalauthsResponse<Texpand = unknown> = Required<ExternalauthsRecord> & BaseSystemFields<Texpand>
export type MfasResponse<Texpand = unknown> = Required<MfasRecord> & BaseSystemFields<Texpand>
export type OtpsResponse<Texpand = unknown> = Required<OtpsRecord> & BaseSystemFields<Texpand>
export type SuperusersResponse<Texpand = unknown> = Required<SuperusersRecord> & AuthSystemFields<Texpand>
export type ActivityLogsResponse<Tdata = unknown, Texpand = unknown> = Required<ActivityLogsRecord<Tdata>> & BaseSystemFields<Texpand>
export type BarristersResponse<Texpand = unknown> = Required<BarristersRecord> & BaseSystemFields<Texpand>
export type ClaimFundingsResponse<Texpand = unknown> = Required<ClaimFundingsRecord> & BaseSystemFields<Texpand>
export type ClaimsResponse<Taudit_trail = unknown, Texpand = unknown> = Required<ClaimsRecord<Taudit_trail>> & BaseSystemFields<Texpand>
export type CoFundersResponse<Tassets_portfolio = unknown, Tbank_account_details = unknown, Tlevel_test_scores = unknown, Texpand = unknown> = Required<CoFundersRecord<Tassets_portfolio, Tbank_account_details, Tlevel_test_scores>> & BaseSystemFields<Texpand>
export type EducationalContentResponse<Tcontent = unknown, Texpand = unknown> = Required<EducationalContentRecord<Tcontent>> & BaseSystemFields<Texpand>
export type EnquiriesResponse<Texpand = unknown> = Required<EnquiriesRecord> & BaseSystemFields<Texpand>
export type ErrorLogsResponse<Tdata = unknown, Texpand = unknown> = Required<ErrorLogsRecord<Tdata>> & BaseSystemFields<Texpand>
export type ExpertWitnessesResponse<Texpand = unknown> = Required<ExpertWitnessesRecord> & BaseSystemFields<Texpand>
export type KnowledgeTestsResponse<Texpand = unknown> = Required<KnowledgeTestsRecord> & BaseSystemFields<Texpand>
export type NotificationsResponse<Tdata = unknown, Texpand = unknown> = Required<NotificationsRecord<Tdata>> & BaseSystemFields<Texpand>
export type PaymentsResponse<Texpand = unknown> = Required<PaymentsRecord> & BaseSystemFields<Texpand>
export type SecurityLogsResponse<Tdata = unknown, Texpand = unknown> = Required<SecurityLogsRecord<Tdata>> & BaseSystemFields<Texpand>
export type SolicitorsResponse<Texpand = unknown> = Required<SolicitorsRecord> & BaseSystemFields<Texpand>
export type TestAttemptsResponse<Texpand = unknown> = Required<TestAttemptsRecord> & BaseSystemFields<Texpand>
export type UserProfilesResponse<Texpand = unknown> = Required<UserProfilesRecord> & BaseSystemFields<Texpand>
export type UsersResponse<Texpand = unknown> = Required<UsersRecord> & AuthSystemFields<Texpand>

// Types containing all Records and Responses, useful for creating typing helper functions

export type CollectionRecords = {
	_authOrigins: AuthoriginsRecord
	_externalAuths: ExternalauthsRecord
	_mfas: MfasRecord
	_otps: OtpsRecord
	_superusers: SuperusersRecord
	activity_logs: ActivityLogsRecord
	barristers: BarristersRecord
	claim_fundings: ClaimFundingsRecord
	claims: ClaimsRecord
	co_funders: CoFundersRecord
	educational_content: EducationalContentRecord
	enquiries: EnquiriesRecord
	error_logs: ErrorLogsRecord
	expert_witnesses: ExpertWitnessesRecord
	knowledge_tests: KnowledgeTestsRecord
	notifications: NotificationsRecord
	payments: PaymentsRecord
	security_logs: SecurityLogsRecord
	solicitors: SolicitorsRecord
	test_attempts: TestAttemptsRecord
	user_profiles: UserProfilesRecord
	users: UsersRecord
}

export type CollectionResponses = {
	_authOrigins: AuthoriginsResponse
	_externalAuths: ExternalauthsResponse
	_mfas: MfasResponse
	_otps: OtpsResponse
	_superusers: SuperusersResponse
	activity_logs: ActivityLogsResponse
	barristers: BarristersResponse
	claim_fundings: ClaimFundingsResponse
	claims: ClaimsResponse
	co_funders: CoFundersResponse
	educational_content: EducationalContentResponse
	enquiries: EnquiriesResponse
	error_logs: ErrorLogsResponse
	expert_witnesses: ExpertWitnessesResponse
	knowledge_tests: KnowledgeTestsResponse
	notifications: NotificationsResponse
	payments: PaymentsResponse
	security_logs: SecurityLogsResponse
	solicitors: SolicitorsResponse
	test_attempts: TestAttemptsResponse
	user_profiles: UserProfilesResponse
	users: UsersResponse
}

// Type for usage with type asserted PocketBase instance
// https://github.com/pocketbase/js-sdk#specify-typescript-definitions

export type TypedPocketBase = PocketBase & {
	collection(idOrName: '_authOrigins'): RecordService<AuthoriginsResponse>
	collection(idOrName: '_externalAuths'): RecordService<ExternalauthsResponse>
	collection(idOrName: '_mfas'): RecordService<MfasResponse>
	collection(idOrName: '_otps'): RecordService<OtpsResponse>
	collection(idOrName: '_superusers'): RecordService<SuperusersResponse>
	collection(idOrName: 'activity_logs'): RecordService<ActivityLogsResponse>
	collection(idOrName: 'barristers'): RecordService<BarristersResponse>
	collection(idOrName: 'claim_fundings'): RecordService<ClaimFundingsResponse>
	collection(idOrName: 'claims'): RecordService<ClaimsResponse>
	collection(idOrName: 'co_funders'): RecordService<CoFundersResponse>
	collection(idOrName: 'educational_content'): RecordService<EducationalContentResponse>
	collection(idOrName: 'enquiries'): RecordService<EnquiriesResponse>
	collection(idOrName: 'error_logs'): RecordService<ErrorLogsResponse>
	collection(idOrName: 'expert_witnesses'): RecordService<ExpertWitnessesResponse>
	collection(idOrName: 'knowledge_tests'): RecordService<KnowledgeTestsResponse>
	collection(idOrName: 'notifications'): RecordService<NotificationsResponse>
	collection(idOrName: 'payments'): RecordService<PaymentsResponse>
	collection(idOrName: 'security_logs'): RecordService<SecurityLogsResponse>
	collection(idOrName: 'solicitors'): RecordService<SolicitorsResponse>
	collection(idOrName: 'test_attempts'): RecordService<TestAttemptsResponse>
	collection(idOrName: 'user_profiles'): RecordService<UserProfilesResponse>
	collection(idOrName: 'users'): RecordService<UsersResponse>
}
