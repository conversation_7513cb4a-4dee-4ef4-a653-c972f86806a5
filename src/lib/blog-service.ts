import type { TypedPocketBase } from '../data/pocketbase-types.ts'
import PocketBase from 'pocketbase'
import { blogCache as advancedCache, CacheKeys } from './blog-cache.ts'

// Blog post interface that matches our current blog structure
export interface BlogPost {
  id: string
  slug: string
  title: string
  summary: string
  content: string
  datePublished: string
  dateUpdated?: string
  metaImage: string
  author: string
  metaAuthor: string
  previousPost?: string
  previousPostTitle?: string
  nextPost?: string
  nextPostTitle?: string
  tags?: string[]
  featured?: boolean
  viewsCount?: number
}

// PocketBase content_items record interface
export interface ContentItemRecord {
  id: string
  type: string
  title: string
  slug: string
  summary?: string
  body_content?: string
  meta_image?: string
  thumbnail_image?: string
  author?: string
  meta_author?: string
  date_published?: string
  date_updated?: string
  previous_post?: string
  previous_post_title?: string
  next_post?: string
  next_post_title?: string
  published_at?: string
  status?: string
  tags?: string[]
  analytics_views?: number
  created: string
  updated: string
}

// Legacy cache - now using advanced cache from blog-cache.ts

// Error types for better error handling
export class BlogServiceError extends Error {
  constructor(message: string, public code: string, public originalError?: Error) {
    super(message)
    this.name = 'BlogServiceError'
  }
}

// Initialize PocketBase client
function createPocketBaseClient(): TypedPocketBase {
  return new PocketBase(
    import.meta.env.POCKETBASE_URL || process.env.POCKETBASE_URL
  ) as TypedPocketBase
}

// Helper function to generate PocketBase file URL
function getPocketBaseFileUrl(record: ContentItemRecord, filename: string): string {
  if (!filename) return ''

  const baseUrl = import.meta.env.POCKETBASE_URL || process.env.POCKETBASE_URL || 'https://3paydb-new-production.up.railway.app'
  return `${baseUrl}/api/files/content_items/${record.id}/${filename}`
}

// Transform PocketBase record to BlogPost format
function transformContentItemToBlogPost(item: ContentItemRecord): BlogPost {
  // Use thumbnail_image if available, fallback to meta_image, then to a default
  let metaImage = ''
  if (item.thumbnail_image) {
    metaImage = getPocketBaseFileUrl(item, item.thumbnail_image)
  } else if (item.meta_image) {
    metaImage = item.meta_image
  } else {
    // Use a placeholder or empty string - the components will handle the fallback
    metaImage = ''
  }

  return {
    id: item.id,
    slug: item.slug,
    title: item.title,
    summary: item.summary || '',
    content: item.body_content || '',
    datePublished: item.date_published || item.published_at || item.created,
    dateUpdated: item.date_updated || item.updated,
    metaImage: metaImage,
    author: item.author || 'admin',
    metaAuthor: item.meta_author || '',
    previousPost: item.previous_post,
    previousPostTitle: item.previous_post_title,
    nextPost: item.next_post,
    nextPostTitle: item.next_post_title,
    tags: Array.isArray(item.tags) ? item.tags : [],
    viewsCount: item.analytics_views || 0
  }
}

// Get all published blog posts
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  const cacheKey = CacheKeys.allPosts()

  // Check advanced cache first
  const cached = advancedCache.get<BlogPost[]>(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const pb = createPocketBaseClient()

    const records = await pb.collection('content_items').getFullList<ContentItemRecord>({
      filter: "type = 'blog' && status = 'published'",
      sort: '-date_published,-published_at,-created'
    })

    const blogPosts = records.map(transformContentItemToBlogPost)

    // Cache the results with advanced cache
    advancedCache.set(cacheKey, blogPosts)

    return blogPosts
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    throw new BlogServiceError(
      'Failed to fetch blog posts',
      'FETCH_ERROR',
      error as Error
    )
  }
}

// Get a single blog post by slug
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  const cacheKey = CacheKeys.postBySlug(slug)

  // Check advanced cache first
  const cached = advancedCache.get<BlogPost>(cacheKey)
  if (cached) {
    return cached
  }

  try {
    const pb = createPocketBaseClient()

    const record = await pb.collection('content_items').getFirstListItem<ContentItemRecord>(
      `type = 'blog' && status = 'published' && slug = '${slug}'`
    )

    const blogPost = transformContentItemToBlogPost(record)

    // Cache the result with advanced cache
    advancedCache.set(cacheKey, blogPost)

    return blogPost
  } catch (error) {
    if (error instanceof Error && error.message.includes('not found')) {
      return null
    }

    console.error(`Error fetching blog post with slug ${slug}:`, error)
    throw new BlogServiceError(
      `Failed to fetch blog post: ${slug}`,
      'FETCH_ERROR',
      error as Error
    )
  }
}

// Get recent blog posts (for homepage, etc.)
export async function getRecentBlogPosts(limit: number = 3): Promise<BlogPost[]> {
  try {
    const allPosts = await getAllBlogPosts()
    return allPosts.slice(0, limit)
  } catch (error) {
    console.error('Error fetching recent blog posts:', error)
    throw new BlogServiceError(
      'Failed to fetch recent blog posts',
      'FETCH_ERROR',
      error as Error
    )
  }
}

// Clear cache (useful for development or when content is updated)
export function clearBlogCache(): void {
  advancedCache.clear()
}

// Get cache status (for debugging)
export function getBlogCacheStatus(): { size: number; keys: string[] } {
  return advancedCache.getStats()
}