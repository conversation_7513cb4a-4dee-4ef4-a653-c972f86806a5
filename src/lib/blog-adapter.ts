import type { BlogPost } from './blog-service.ts'

// Content component type
export interface ContentComponent {
  render(): string
}

// Astro content collection compatible interface
export interface AstroContentPost {
  slug: string
  data: {
    itemId: string
    title: string
    datePublished: string
    dateUpdated?: string
    metaImage: string
    author: string
    metaAuthor: string
    previousPost?: string
    previousPostTitle?: string
    nextPost?: string
    nextPostTitle?: string
    link?: string
    summary: string
  }
  render: () => Promise<{ Content: ContentComponent }>
}

// Mock content component for rendering markdown/HTML content
class MockContentComponent implements ContentComponent {
  constructor(private content: string) {}

  // This will be used to render the content in Astro components
  render(): string {
    return this.content
  }
}

// Transform BlogPost to Astro content collection format
export function transformBlogPostToAstroContent(post: BlogPost): AstroContentPost {
  return {
    slug: post.slug,
    data: {
      itemId: `/${post.slug}/`,
      title: post.title,
      datePublished: post.datePublished,
      dateUpdated: post.dateUpdated,
      metaImage: post.metaImage,
      author: post.author,
      metaAuthor: post.metaAuthor,
      previousPost: post.previousPost,
      previousPostTitle: post.previousPostTitle,
      nextPost: post.nextPost,
      nextPostTitle: post.nextPostTitle,
      link: `/${post.slug}/`,
      summary: post.summary
    },
    render: () => Promise.resolve({
      Content: new MockContentComponent(post.content)
    })
  }
}

// Transform array of BlogPosts to Astro content collection format
export function transformBlogPostsToAstroContent(posts: BlogPost[]): AstroContentPost[] {
  return posts.map(transformBlogPostToAstroContent)
}

// Fallback data for when blog service fails
export function createFallbackBlogPost(slug: string): AstroContentPost {
  return {
    slug,
    data: {
      itemId: `/${slug}/`,
      title: 'Blog Post Unavailable',
      datePublished: new Date().toISOString(),
      metaImage: '', // Empty string - let the component handle the fallback
      author: 'admin',
      metaAuthor: 'https://3payglobal.com/author/admin/',
      summary: 'This blog post is temporarily unavailable. Please try again later.'
    },
    render: () => Promise.resolve({
      Content: new MockContentComponent('<p>This blog post is temporarily unavailable. Please try again later.</p>')
    })
  }
}

// Error boundary component for blog content
export function createErrorBlogPost(error: Error, slug?: string): AstroContentPost {
  const errorSlug = slug || 'error'
  return {
    slug: errorSlug,
    data: {
      itemId: `/${errorSlug}/`,
      title: 'Error Loading Blog Post',
      datePublished: new Date().toISOString(),
      metaImage: '', // Empty string - let the component handle the fallback
      author: 'system',
      metaAuthor: 'https://3payglobal.com/',
      summary: 'An error occurred while loading this blog post.'
    },
    render: () => Promise.resolve({
      Content: new MockContentComponent(`
        <div class="error-message">
          <h2>Error Loading Content</h2>
          <p>We're sorry, but there was an error loading this blog post.</p>
          <p>Error: ${error.message}</p>
          <p>Please try refreshing the page or contact support if the problem persists.</p>
        </div>
      `)
    })
  }
}

// Utility to format dates consistently
export function formatBlogDate(dateString: string): string {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-us', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch (error) {
    console.error('Error formatting date:', error)
    return dateString
  }
}

// Utility to extract excerpt from content
export function extractExcerpt(content: string, maxLength: number = 150): string {
  // Remove HTML tags
  const textContent = content.replace(/<[^>]*>/g, '')

  if (textContent.length <= maxLength) {
    return textContent
  }

  // Find the last complete word within the limit
  const truncated = textContent.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')

  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }

  return truncated + '...'
}