import type { BlogPost } from './blog-service.ts'

// Cache configuration
export interface CacheConfig {
  defaultTTL: number // Time to live in milliseconds
  maxSize: number    // Maximum number of items in cache
  enablePersistence: boolean // Whether to persist cache to localStorage
}

// Cache entry interface
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

// Advanced cache implementation with LRU eviction and persistence
export class BlogCache {
  private cache = new Map<string, CacheEntry<any>>()
  private config: CacheConfig

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      maxSize: 100,
      enablePersistence: false, // Disabled by default for SSR
      ...config
    }

    // Load from localStorage if persistence is enabled and we're in browser
    if (this.config.enablePersistence && typeof window !== 'undefined') {
      this.loadFromStorage()
    }
  }

  // Set cache entry with optional TTL
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      accessCount: 0,
      lastAccessed: Date.now()
    }

    // If cache is full, remove least recently used item
    if (this.cache.size >= this.config.maxSize) {
      this.evictLRU()
    }

    this.cache.set(key, entry)

    // Persist to localStorage if enabled
    if (this.config.enablePersistence) {
      this.saveToStorage()
    }
  }

  // Get cache entry
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined

    if (!entry) {
      return null
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      if (this.config.enablePersistence) {
        this.saveToStorage()
      }
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = Date.now()

    return entry.data
  }

  // Check if key exists and is valid
  has(key: string): boolean {
    return this.get(key) !== null
  }

  // Delete specific key
  delete(key: string): boolean {
    const result = this.cache.delete(key)
    if (result && this.config.enablePersistence) {
      this.saveToStorage()
    }
    return result
  }

  // Clear all cache
  clear(): void {
    this.cache.clear()
    if (this.config.enablePersistence) {
      this.saveToStorage()
    }
  }

  // Get cache statistics
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    keys: string[]
  } {
    const entries = Array.from(this.cache.values())
    const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0)
    const hits = entries.filter(entry => entry.accessCount > 0).length

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: totalAccesses > 0 ? hits / totalAccesses : 0,
      keys: Array.from(this.cache.keys())
    }
  }

  // Evict least recently used item
  private evictLRU(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  // Save cache to localStorage
  private saveToStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const cacheData = Array.from(this.cache.entries())
      localStorage.setItem('blog-cache', JSON.stringify(cacheData))
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error)
    }
  }

  // Load cache from localStorage
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('blog-cache')
      if (stored) {
        const cacheData = JSON.parse(stored) as [string, CacheEntry<unknown>][]

        // Filter out expired entries
        const now = Date.now()
        const validEntries = cacheData.filter(([_, entry]) =>
          now - entry.timestamp <= entry.ttl
        )

        this.cache = new Map(validEntries)
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error)
    }
  }
}

// Global cache instance
export const blogCache = new BlogCache({
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 50,
  enablePersistence: false // Keep disabled for SSR
})

// Cache key generators
export const CacheKeys = {
  allPosts: () => 'blog:all-posts',
  postBySlug: (slug: string) => `blog:post:${slug}`,
  recentPosts: (limit: number) => `blog:recent:${limit}`,
  postsByTag: (tag: string) => `blog:tag:${tag}`,
  postsByAuthor: (author: string) => `blog:author:${author}`
}