---
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
---

<Layout title="Co-Funders - 3Pay Global">
	<div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-gradient-to-br from-[#003820]/5 via-white to-[#f0f7f4]/30">
			<!-- Enhanced background effects -->
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
				<!-- Floating particles -->
				<div class="absolute top-1/4 left-1/3 w-2 h-2 bg-[#003820]/20 rounded-full animate-float-particle"></div>
				<div class="absolute top-3/4 right-1/3 w-3 h-3 bg-[#003820]/15 rounded-full animate-float-particle-delayed"></div>
				<div class="absolute bottom-1/4 left-2/3 w-1 h-1 bg-[#003820]/25 rounded-full animate-float-particle-slow"></div>
			</div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto text-center">
					<!-- Enhanced header with better typography -->
					<div class="mb-8 mt-8">
						<div class="inline-flex items-center bg-gradient-to-r from-[#003820]/10 to-[#003820]/5 backdrop-blur-sm px-6 py-3 rounded-full hover:scale-105 transition-all duration-300 hover:shadow-lg mb-6">
							<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" class="w-5 h-5 mr-3 text-[#003820]">
								<path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							<span class="text-base font-medium text-[#003820]">Exclusive Opportunity</span>
						</div>
					</div>
					
					<h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 mt-12 animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820] leading-tight">
						Put Your Money to Work Like Never Before
					</h1>
					<h2 class="text-3xl md:text-4xl lg:text-5xl font-semibold mb-12 text-[#003820] animate-pulse">
						Break Free from Mediocre Returns
					</h2>
					
					<div class="prose prose-lg max-w-4xl mx-auto text-[#3C4145]/90 space-y-8">
						<p class="mb-8 text-xl md:text-2xl leading-relaxed animate-fade-in">
							Are you frustrated by the low returns from traditional savings or bank accounts? It's time to step into an exclusive opportunity to grow your capital in a smarter, more impactful way. At 3Pay Global, we offer forward-thinking individuals the chance to become co-funders in high-value litigation funding, <span class="text-[#003820] font-semibold">a dynamic asset class designed to deliver exceptional results</span>.
						</p>
						<p class="mb-8 text-lg md:text-xl leading-relaxed animate-fade-in delay-100">
							Here's how it works: we partner with the claimant's solicitor firm to ensure all pre-agreed legal disbursement costs are seamlessly covered at every stage. This efficient, results-driven approach keeps cases on track and focused on winning.
						</p>
						<p class="mb-8 text-lg md:text-xl leading-relaxed animate-fade-in delay-200">
							Don't just settle for the ordinary. Be part of something extraordinary. Join a select group of co-funders transforming how wealth grows, and make your capital a driving force behind real success.
						</p>
					</div>
					
					<div class="mt-16 animate-slide-up">
						<a href="https://app.3payglobal.com/#/register-cofunder" target="_blank" class="submit-button inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white rounded-2xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
							Join as Co-Funder
							<svg class="w-6 h-6 ml-3 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
							</svg>
						</a>
					</div>
				</div>
			</div>
			<div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-[#003820]/20 to-transparent pointer-events-none"></div>
		</section>

		<!-- A Smarter Approach Section -->
		<section class="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-slow"></div>
			<!-- Enhanced decorative elements -->
			<div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-[#003820]/10 to-transparent rounded-full blur-3xl"></div>
			<div class="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-[#003820]/8 to-transparent rounded-full blur-2xl"></div>
			
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-6xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							A Smarter Approach to Wealth Growth with Proven Results
						</h2>
					</div>
					
					<div class="grid lg:grid-cols-2 gap-16 items-center">
						<div class="space-y-8">
							<div class="prose prose-lg text-[#3C4145]/90">
								<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-100">
									At 3Pay, we do things differently. We work with top-tier legal teams and back only the strongest claims. From the outset, we agree to pursue litigation where the solicitors fees is paid under a conditional fee arrangement, while 3Pay covers 100% of the pre-agreed legal disbursement costs up to £3,000,000.
								</p>
								<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-200">
									By partnering with 3Pay, you're putting your capital to work in a proven system designed for success, offering returns that could far exceed what traditional methods provide.
								</p>
							</div>
							
							<div class="mt-12 animate-slide-up">
								<a href="/learn-more" class="learn-more-button inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white rounded-xl hover:shadow-lg transition-all duration-500">
									Learn More
									<svg class="w-5 h-5 ml-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
									</svg>
								</a>
							</div>
						</div>
						
						<!-- Enhanced visual element -->
						<div class="relative">
							<div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-[#003820]/10">
								<div class="space-y-6">
									<div class="flex items-center space-x-4">
										<div class="w-12 h-12 bg-gradient-to-br from-[#003820] to-[#184E35] rounded-xl flex items-center justify-center">
											<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
											</svg>
										</div>
										<div>
											<h3 class="text-xl font-semibold text-[#003820]">Up to £3M Coverage</h3>
											<p class="text-[#3C4145]/80">Full legal disbursement funding</p>
										</div>
									</div>
									
									<div class="flex items-center space-x-4">
										<div class="w-12 h-12 bg-gradient-to-br from-[#184E35] to-[#243255] rounded-xl flex items-center justify-center">
											<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
											</svg>
										</div>
										<div>
											<h3 class="text-xl font-semibold text-[#003820]">Top-Tier Legal Teams</h3>
											<p class="text-[#3C4145]/80">Only the strongest claims</p>
										</div>
									</div>
									
									<div class="flex items-center space-x-4">
										<div class="w-12 h-12 bg-gradient-to-br from-[#243255] to-[#003820] rounded-xl flex items-center justify-center">
											<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
											</svg>
										</div>
										<div>
											<h3 class="text-xl font-semibold text-[#003820]">Exceptional Returns</h3>
											<p class="text-[#3C4145]/80">Far exceed traditional methods</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Dynamic Asset Class Section -->
		<section class="py-24 bg-gradient-to-br from-white to-[#f0f7f4]/50 relative overflow-hidden">
			<div class="absolute inset-0 bg-gradient-to-r from-[#003820]/10 to-transparent mix-blend-multiply pointer-events-none"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-6xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-12 text-[#003820] animate-fade-in">
						Dynamic Asset Class
					</h2>
					
					<div class="grid md:grid-cols-2 gap-12 items-center">
						<div class="space-y-8 text-left">
							<div class="prose prose-lg text-[#3C4145]/90">
								<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-100">
									In 2024, litigation funding quietly outperformed traditional savings options, delivering remarkable returns that few are fortunate enough to access. This isn't just a financial opportunity, it's your chance to be part of something truly exceptional, a select asset class that's redefining what's possible for your money.
								</p>
								<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-200">
									This isn't for everyone. It's for those who recognise the power of making their capital work harder and smarter, in ways that traditional methods simply can't match. While others settle for low returns, you could be positioning yourself at the forefront of a rapidly growing market with incredible potential.
								</p>
								<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-300">
									Litigation funding isn't just another investment option; it's an exclusive opportunity for those who demand more from their money. With the chance to earn up to 75% of your capital, this is an opportunity you won't want to let pass by.
								</p>
							</div>
						</div>
						
						<!-- Enhanced comparison chart -->
						<div class="relative">
							<div class="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-[#003820]/10">
								<h3 class="text-2xl font-bold text-[#003820] mb-8 text-center">Return Comparison</h3>
								<div class="space-y-6">
									<div class="flex items-center justify-between p-4 bg-red-50 rounded-xl">
										<span class="text-gray-600">Traditional Savings</span>
										<span class="text-red-600 font-bold">0.5-2%</span>
									</div>
									<div class="flex items-center justify-between p-4 bg-yellow-50 rounded-xl">
										<span class="text-gray-600">Stock Market (avg)</span>
										<span class="text-yellow-600 font-bold">7-10%</span>
									</div>
									<div class="flex items-center justify-between p-4 bg-green-50 rounded-xl border-2 border-[#003820]/20">
										<span class="text-[#003820] font-semibold">3Pay Litigation Funding</span>
										<span class="text-[#003820] font-bold text-xl">5-75%</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- WHY 3PAY? Section -->
		<section class="py-24 bg-gradient-to-br from-gray-100 to-gray-50 relative overflow-hidden">
			<div class="absolute inset-0 bg-[#003820]/5 animate-rotate-reverse-slow"></div>
			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-7xl mx-auto">
					<div class="text-center mb-16">
						<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-[#003820] animate-fade-in">
							WHY 3PAY?
						</h2>
						<p class="text-xl text-[#3C4145]/80 max-w-3xl mx-auto">
							3Pay has a track record for working alongside established and reputable law firms, dedicated to securing funding for their clients. Moreover, dozens of litigants and their law firms have benefitted from successful outcomes as a direct result of the millions of pounds 3Pay has already committed to funding litigation claims in recent years.
						</p>
					</div>

					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
						<!-- Success Rate Card -->
						<div class="tilt-card bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-[#003820]/10">
							<div class="relative">
								<div class="absolute -top-4 -left-4 w-16 h-16 bg-[#003820]/10 rounded-full blur-lg animate-pulse-slow"></div>
								<div class="relative z-10 text-center">
									<div class="text-5xl font-bold text-[#003820] mb-4">100%</div>
									<h3 class="text-xl font-semibold mb-4 text-[#003820]">Success Rate</h3>
									<p class="text-[#3C4145]/80">Over the past 8 years</p>
								</div>
							</div>
						</div>

						<!-- Returns Card -->
						<div class="tilt-card bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-[#003820]/10">
							<div class="relative">
								<div class="absolute -top-4 -right-4 w-16 h-16 bg-[#003820]/10 rounded-full blur-lg animate-pulse-slow delay-150"></div>
								<div class="relative z-10 text-center">
									<div class="text-5xl font-bold text-[#003820] mb-4">5-75%</div>
									<h3 class="text-xl font-semibold mb-4 text-[#003820]">Return Rate</h3>
									<p class="text-[#3C4145]/80">On successful claims</p>
								</div>
							</div>
						</div>

						<!-- Low Hanging Fruits Card -->
						<div class="tilt-card bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-[#003820]/10">
							<div class="relative">
								<div class="absolute -bottom-4 -left-4 w-16 h-16 bg-[#003820]/10 rounded-full blur-lg animate-pulse-slow delay-300"></div>
								<div class="relative z-10 text-center">
									<div class="mb-4">
										<svg class="w-12 h-12 text-[#003820] mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
										</svg>
									</div>
									<h3 class="text-xl font-semibold mb-4 text-[#003820]">Low Hanging Fruits</h3>
									<p class="text-[#3C4145]/80">Only committed to strong cases</p>
								</div>
							</div>
						</div>

						<!-- Due Diligence Card -->
						<div class="tilt-card bg-white rounded-3xl shadow-xl p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-[#003820]/10">
							<div class="relative">
								<div class="absolute -bottom-4 -right-4 w-16 h-16 bg-[#003820]/10 rounded-full blur-lg animate-pulse-slow delay-450"></div>
								<div class="relative z-10 text-center">
									<div class="mb-4">
										<svg class="w-12 h-12 text-[#003820] mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
										</svg>
									</div>
									<h3 class="text-xl font-semibold mb-4 text-[#003820]">Independent Due Diligence</h3>
									<p class="text-[#3C4145]/80">By lawyers acting independently</p>
								</div>
							</div>
						</div>
					</div>

					<!-- Case Study -->
					<div class="bg-gradient-to-r from-[#003820]/5 to-[#003820]/10 rounded-3xl p-8 md:p-12">
						<div class="max-w-4xl mx-auto text-center">
							<h3 class="text-2xl md:text-3xl font-bold text-[#003820] mb-6">Success Story</h3>
							<div class="prose prose-lg text-[#3C4145]/90 mx-auto">
								<p class="text-lg leading-relaxed">
									3Pay provided <span class="font-bold text-[#003820]">£3m in litigation funding</span> for claimants' to successfully litigate against Zurich Insurance company, with a claim for <span class="font-bold text-[#003820]">£25million</span>. The claim was settled in 2022, within less than 18 months, and the case garnered press attention and was featured in the UK's Sunday Times.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Act Now Section -->
		<section class="py-24 bg-gradient-to-br from-white to-[#f0f7f4] relative overflow-hidden">
			<div class="absolute top-0 right-0 w-96 h-96 rounded-full bg-[#003820]/10 filter blur-3xl animate-float-slow"></div>
			<div class="absolute bottom-0 left-0 w-72 h-72 rounded-full bg-[#003820]/8 filter blur-2xl animate-float-reverse"></div>

			<div class="container mx-auto px-4 relative z-10">
				<div class="max-w-5xl mx-auto text-center">
					<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-12 text-[#003820] animate-fade-in">
						Act Now, Opportunities Are Limited
					</h2>
					<div class="prose prose-lg max-w-4xl mx-auto text-[#3C4145]/90 mb-16">
						<p class="mb-8 text-xl leading-relaxed animate-fade-in delay-100">
							Litigation funding is a dynamic and exclusive asset class that's outperforming traditional investments. Few people get access to this level of opportunity. Don't settle for low returns when you could be part of something far more rewarding.
						</p>
						<p class="mb-8 text-lg leading-relaxed animate-fade-in delay-200">
							Join 3Pay Global today and make your capital work smarter.
						</p>
					</div>

					<div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up">
						<a href="https://app.3payglobal.com/#/register-cofunder" target="_blank" class="submit-button inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-white rounded-2xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
							Join as Co-Funder
							<svg class="w-6 h-6 ml-3 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"/>
							</svg>
						</a>
						<a href="mailto:<EMAIL>" class="contact-button inline-flex items-center justify-center px-10 py-5 text-lg font-semibold text-[#003820] bg-white border-2 border-[#003820] rounded-2xl hover:bg-[#003820] hover:text-white transition-all duration-500 transform hover:scale-105">
							<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
							</svg>
							Contact Us
						</a>
					</div>
				</div>
			</div>
		</section>
	</div>
</Layout>

<style>
	/* Premium grid background */
	.bg-grid {
		background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
			linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
		background-size: 32px 32px;
		background-position: center center;
	}

	/* Enhanced button styles */
	.submit-button {
		background: linear-gradient(135deg, #003820, #005732, #184E35, #243255);
		background-size: 300% 300%;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 8px 32px rgba(0, 56, 32, 0.3);
		position: relative;
		overflow: hidden;
	}

	.submit-button::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
		transition: left 0.5s;
	}

	.submit-button:hover::before {
		left: 100%;
	}

	.submit-button:hover {
		background-position: right center;
		transform: translateY(-4px) scale(1.05);
		box-shadow: 0 16px 48px rgba(0, 56, 32, 0.4);
	}

	.submit-button:hover svg {
		transform: translateX(6px);
	}

	.learn-more-button {
		background: linear-gradient(135deg, #005732, #003820, #184E35);
		background-size: 200% auto;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 4px 16px rgba(0, 56, 32, 0.2);
	}

	.learn-more-button:hover {
		background-position: right center;
		transform: translateY(-2px);
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.3);
	}

	.learn-more-button:hover svg {
		transform: translateX(4px);
	}

	.contact-button {
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 4px 16px rgba(0, 56, 32, 0.1);
	}

	.contact-button:hover {
		box-shadow: 0 8px 24px rgba(0, 56, 32, 0.2);
	}

	/* Enhanced animations */
	.animate-fade-in {
		animation: fadeIn 0.8s ease-out forwards;
		opacity: 0;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(30px); }
		to { opacity: 1; transform: translateY(0); }
	}

	.animate-slide-up {
		animation: slideUp 0.8s ease-out forwards;
		opacity: 0;
	}

	@keyframes slideUp {
		from { opacity: 0; transform: translateY(60px); }
		to { opacity: 1; transform: translateY(0); }
	}

	/* Animation delays */
	.delay-100 { animation-delay: 0.1s; }
	.delay-200 { animation-delay: 0.2s; }
	.delay-300 { animation-delay: 0.3s; }
	.delay-450 { animation-delay: 0.45s; }

	/* Enhanced pulse animation */
	.animate-pulse {
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	@keyframes pulse {
		0%, 100% { opacity: 1; }
		50% { opacity: 0.8; }
	}

	.animate-pulse-slow {
		animation: pulseSlow 3s ease-in-out infinite;
	}

	@keyframes pulseSlow {
		0%, 100% { opacity: 0.1; transform: scale(1); }
		50% { opacity: 0.3; transform: scale(1.1); }
	}

	/* Floating animations */
	.animate-float-slow {
		animation: float 8s ease-in-out infinite;
	}

	.animate-float-reverse {
		animation: floatReverse 10s ease-in-out infinite;
	}

	.animate-float-particle {
		animation: floatParticle 6s ease-in-out infinite;
	}

	.animate-float-particle-delayed {
		animation: floatParticle 8s ease-in-out infinite;
		animation-delay: 2s;
	}

	.animate-float-particle-slow {
		animation: floatParticle 12s ease-in-out infinite;
		animation-delay: 4s;
	}

	@keyframes float {
		0%, 100% { transform: translateY(0) rotate(0deg); }
		33% { transform: translateY(-20px) rotate(1deg); }
		66% { transform: translateY(-10px) rotate(-1deg); }
	}

	@keyframes floatReverse {
		0%, 100% { transform: translateY(0) rotate(0deg); }
		33% { transform: translateY(15px) rotate(-1deg); }
		66% { transform: translateY(8px) rotate(1deg); }
	}

	@keyframes floatParticle {
		0%, 100% { transform: translateY(0) translateX(0); opacity: 0.3; }
		25% { transform: translateY(-30px) translateX(10px); opacity: 0.7; }
		50% { transform: translateY(-20px) translateX(-5px); opacity: 0.5; }
		75% { transform: translateY(-40px) translateX(15px); opacity: 0.8; }
	}

	/* Rotate animations */
	.animate-rotate-slow {
		animation: rotate 30s linear infinite;
	}

	.animate-rotate-reverse-slow {
		animation: rotateReverse 25s linear infinite;
	}

	@keyframes rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}

	@keyframes rotateReverse {
		from { transform: rotate(360deg); }
		to { transform: rotate(0deg); }
	}

	/* Enhanced gradient animation */
	.animate-gradient {
		animation: gradient 3s ease infinite;
	}

	@keyframes gradient {
		0%, 100% { background-position: 0% 50%; }
		50% { background-position: 100% 50%; }
	}

	/* 3D Tilt Effect */
	.tilt-card {
		transform-style: preserve-3d;
		perspective: 1000px;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.tilt-card:hover {
		transform:
			translateY(-12px)
			rotateX(var(--rotate-x, 0deg))
			rotateY(var(--rotate-y, 0deg));
	}

	/* Responsive improvements */
	@media (max-width: 768px) {
		.submit-button, .contact-button {
			width: 100%;
			max-width: 300px;
		}
	}
</style>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const cards = document.querySelectorAll('.tilt-card');

		cards.forEach(card => {
			card.addEventListener('mousemove', (e) => {
				const mouseEvent = e as MouseEvent;
				const htmlElement = card as HTMLElement;
				const rect = htmlElement.getBoundingClientRect();
				const x = mouseEvent.clientX - rect.left;
				const y = mouseEvent.clientY - rect.top;

				const centerX = rect.width / 2;
				const centerY = rect.height / 2;

				const rotateX = (y - centerY) / 15;
				const rotateY = (centerX - x) / 15;

				htmlElement.style.setProperty('--rotate-x', `${rotateX}deg`);
				htmlElement.style.setProperty('--rotate-y', `${rotateY}deg`);
			});

			card.addEventListener('mouseleave', () => {
				const htmlElement = card as HTMLElement;
				htmlElement.style.setProperty('--rotate-x', '0deg');
				htmlElement.style.setProperty('--rotate-y', '0deg');
			});
		});

		// Enhanced scroll animations
		const observerOptions = {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		};

		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					const htmlElement = entry.target as HTMLElement;
					htmlElement.style.animationPlayState = 'running';
				}
			});
		}, observerOptions);

		document.querySelectorAll('.animate-fade-in, .animate-slide-up').forEach(el => {
			const htmlElement = el as HTMLElement;
			htmlElement.style.animationPlayState = 'paused';
			observer.observe(el);
		});
	});
</script>
