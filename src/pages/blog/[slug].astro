---
export const prerender = false // Switch to SSR for dynamic content
import MarkdownLayout from '../../layouts/MarkdownLayout.astro'
import { getBlogPostBySlug, getAllBlogPosts, BlogServiceError } from '@lib/blog-service.ts'
import { transformBlogPostToAstroContent, createErrorBlogPost, createFallbackBlogPost } from '@lib/blog-adapter.ts'

// For static generation, we still need to provide paths
export async function getStaticPaths() {
  try {
    const posts = await getAllBlogPosts()
    return posts.map((post) => ({
      params: { slug: post.slug },
      props: { slug: post.slug },
    }))
  } catch (error) {
    console.error('Error generating static paths:', error)
    return []
  }
}

// Get the slug from the URL
const { slug } = Astro.params

if (!slug) {
  return Astro.redirect('/blog')
}

// Fetch the blog post from PocketBase
let post;
let hasError = false;

try {
  const blogPost = await getBlogPostBySlug(slug)

  if (!blogPost) {
    // Post not found, return 404
    return new Response(null, {
      status: 404,
      statusText: 'Blog post not found'
    })
  }

  post = transformBlogPostToAstroContent(blogPost)
} catch (error) {
  hasError = true;
  console.error(`Error fetching blog post ${slug}:`, error)

  if (error instanceof BlogServiceError) {
    post = createErrorBlogPost(error, slug)
  } else {
    post = createFallbackBlogPost(slug)
  }
}

const { Content } = await post.render()
---

{hasError && (
  <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
      </svg>
      <p class="text-red-800 text-sm">
        <strong>Error:</strong> There was an issue loading this blog post.
      </p>
    </div>
  </div>
)}

<MarkdownLayout
  title={post.data.title}
  date={post.data.datePublished}
  image={post.data.metaImage}
  previousPost={post.data.previousPost}
  previousPostTitle={post.data.previousPostTitle}
  nextPostTitle={post.data.nextPostTitle}
  nextPost={post.data.nextPost}
>
  <div set:html={Content.render()} />
</MarkdownLayout>