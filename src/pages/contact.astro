---
// Contact Page for 3Pay Global
import Layout from '../layouts/Layout.astro';
import '@styles/animations.css';
---

<Layout title="Contact 3Pay Global - Litigation Funding">
    <div class="relative w-full overflow-hidden">
		<!-- Hero Section -->
		<section class="relative min-h-screen flex items-center justify-center py-20 bg-[#003820]/5">
			<div class="absolute inset-0 overflow-hidden pointer-events-none">
				<div class="absolute top-0 left-1/4 w-[45rem] h-[45rem] bg-gradient-to-br from-[#003820]/10 to-[#003820]/5 rounded-full blur-[160px] mix-blend-soft-light animate-float-slow"></div>
				<div class="absolute bottom-0 right-1/4 w-[35rem] h-[35rem] bg-gradient-to-tr from-[#003820]/15 to-[#003820]/5 rounded-full blur-[128px] mix-blend-soft-light animate-float-reverse"></div>
				<div class="absolute inset-0 bg-grid opacity-[0.015] mix-blend-overlay"></div>
			</div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 mt-12 animate-gradient bg-[length:200%_auto] bg-clip-text text-transparent bg-gradient-to-r from-[#003820] via-[#005732] to-[#003820]">
                    Contact Us
                </h1>
                <p class="text-xl text-[#3C4145] max-w-2xl mx-auto leading-relaxed">
                    Get in touch with our expert team to discuss your litigation funding needs. We're here to help you achieve the best possible outcome for your case.
                </p>
            </div>

      <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto mb-16">
        <!-- Contact Information Card -->
        <div class="lg:col-span-1">
          <div class="premium-card bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-[#003820]/10 hover-lift">
            <div class="card-border absolute inset-0 rounded-2xl"></div>
            <div class="relative z-10">
              <div class="p-8 border-b border-[#003820]/10">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-gradient-to-r from-[#003820] to-[#005732] rounded-xl flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                  </div>
                  <h2 class="text-2xl font-bold text-[#003820]">Our London Office</h2>
                </div>
                <p class="text-[#3C4145] leading-relaxed">Visit us at our central London location for in-person consultations and meetings.</p>
              </div>

              <div class="p-8 space-y-6">
                <div class="contact-item group">
                  <div class="flex items-start">
                    <div class="contact-icon-wrapper">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="contact-icon">
                        <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                        <circle cx="12" cy="10" r="3"></circle>
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h4 class="font-semibold text-[#003820] mb-1">Address</h4>
                      <p class="text-[#3C4145] leading-relaxed">78 York Street<br>London, W1H 1DP<br>United Kingdom</p>
                    </div>
                  </div>
                </div>

                <div class="contact-item group">
                  <div class="flex items-start">
                    <div class="contact-icon-wrapper">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="contact-icon">
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h4 class="font-semibold text-[#003820] mb-1">Phone</h4>
                      <a href="tel:+442076928977" class="text-[#3C4145] hover:text-[#003820] transition-colors duration-300 hover:underline">
                        +44 (2)076 9289 77
                      </a>
                    </div>
                  </div>
                </div>

                <div class="contact-item group">
                  <div class="flex items-start">
                    <div class="contact-icon-wrapper">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="contact-icon">
                        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h4 class="font-semibold text-[#003820] mb-1">Email</h4>
                      <a href="mailto:<EMAIL>" class="text-[#3C4145] hover:text-[#003820] transition-colors duration-300 hover:underline">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>

                <div class="border-t border-[#003820]/10 my-6 pt-6">
                  <h3 class="font-semibold text-[#003820] mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                    Connect With Us
                  </h3>
                  <div class="flex space-x-3">
                    <a href="#" class="social-link" aria-label="Follow us on Facebook">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                      </svg>
                    </a>
                    <a href="#" class="social-link" aria-label="Connect with us on LinkedIn">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                      </svg>
                    </a>
                    <a href="#" class="social-link" aria-label="Follow us on Twitter">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Map and Contact Form Section -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Map Card -->
          <div class="premium-card bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-[#003820]/10 hover-lift">
            <div class="card-border absolute inset-0 rounded-2xl"></div>
            <div class="relative z-10">
              <div class="p-6 border-b border-[#003820]/10">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gradient-to-r from-[#003820] to-[#005732] rounded-lg flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                      <polyline points="14,2 14,8 20,8"></polyline>
                    </svg>
                  </div>
                  <h3 class="text-xl font-bold text-[#003820]">Find Us</h3>
                </div>
              </div>
              <div class="h-[400px] relative">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2482.7662603779584!2d-0.15731492342157214!3d51.51801677181147!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x48761b0289578d47%3A0x4552cd7313cc5c0!2s78%20York%20St%2C%20London%20W1H%201DP%2C%20UK!5e0!3m2!1sen!2sus!4v1647851824288!5m2!1sen!2sus"
                  width="100%"
                  height="100%"
                  style="border: 0;"
                  allowfullscreen
                  loading="lazy"
                  title="3Pay Global office location"
                  class="rounded-b-2xl">
                </iframe>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="premium-card bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl overflow-hidden border border-[#003820]/10 hover-lift">
            <div class="card-border absolute inset-0 rounded-2xl"></div>
            <div class="relative z-10">
              <div class="p-8 border-b border-[#003820]/10">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-gradient-to-r from-[#003820] to-[#005732] rounded-xl flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-2xl font-bold text-[#003820]">Send Us a Message</h2>
                    <p class="text-[#3C4145] mt-1">We'll get back to you within 24 hours</p>
                  </div>
                </div>
              </div>

              <div class="p-8">
                <form id="contactForm" class="space-y-6">
                  <div class="grid md:grid-cols-2 gap-6">
                    <div class="form-group">
                      <label for="name" class="form-label">
                        <span class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                          Full Name *
                        </span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        class="form-input"
                        placeholder="Enter your full name"
                        required
                        aria-describedby="name-error"
                      />
                      <div id="name-error" class="error-message hidden"></div>
                    </div>

                    <div class="form-group">
                      <label for="email" class="form-label">
                        <span class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                          </svg>
                          Email Address *
                        </span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input"
                        placeholder="Enter your email address"
                        required
                        aria-describedby="email-error"
                      />
                      <div id="email-error" class="error-message hidden"></div>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="subject" class="form-label">
                      <span class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                          <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                          <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        Subject *
                      </span>
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      class="form-input"
                      placeholder="What is this regarding?"
                      required
                      aria-describedby="subject-error"
                    />
                    <div id="subject-error" class="error-message hidden"></div>
                  </div>

                  <div class="form-group">
                    <label for="message" class="form-label">
                      <span class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                          <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2v5Z"></path>
                          <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1"></path>
                        </svg>
                        Message *
                      </span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows="5"
                      class="form-input resize-none"
                      placeholder="Tell us about your litigation funding needs..."
                      required
                      aria-describedby="message-error"
                    ></textarea>
                    <div id="message-error" class="error-message hidden"></div>
                  </div>

                  <div class="flex items-center justify-between pt-4">
                    <div class="text-sm text-[#3C4145]">
                      <span class="text-red-500">*</span> Required fields
                    </div>
                    <button
                      type="submit"
                      class="submit-button group"
                      id="submitBtn"
                    >
                      <span class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 transition-transform duration-300 group-hover:translate-x-1">
                          <path d="m22 2-7 20-4-9-9-4Z"></path>
                          <path d="M22 2 11 13"></path>
                        </svg>
                        Send Message
                      </span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  </div>
  </section>

</Layout>

<script>
  // Contact form functionality with enhanced validation and UX
  class ContactFormController {
    constructor() {
      this.form = document.getElementById('contactForm') as HTMLFormElement;
      this.submitBtn = document.getElementById('submitBtn') as HTMLButtonElement;
      this.isSubmitting = false;

      if (this.form) {
        this.init();
      }
    }

    init() {
      this.form.addEventListener('submit', this.handleSubmit.bind(this));
      this.setupRealTimeValidation();
      this.setupAccessibility();
    }

    setupRealTimeValidation() {
      const inputs = this.form.querySelectorAll('input, textarea') as NodeListOf<HTMLInputElement | HTMLTextAreaElement>;

      inputs.forEach(input => {
        input.addEventListener('blur', () => this.validateField(input));
        input.addEventListener('input', () => this.clearError(input));
      });
    }

    setupAccessibility() {
      // Add ARIA live region for form feedback
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.id = 'form-feedback';
      this.form.appendChild(liveRegion);
    }

    validateField(field: HTMLInputElement | HTMLTextAreaElement): boolean {
      const errorElement = document.getElementById(`${field.id}-error`) as HTMLElement;
      let isValid = true;
      let errorMessage = '';

      // Clear previous error state
      this.clearError(field);

      // Required field validation
      if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        errorMessage = `${this.getFieldLabel(field)} is required.`;
      }

      // Email validation
      if (field.type === 'email' && field.value.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value.trim())) {
          isValid = false;
          errorMessage = 'Please enter a valid email address.';
        }
      }

      // Show error if validation failed
      if (!isValid) {
        this.showError(field, errorMessage);
      }

      return isValid;
    }

    clearError(field: HTMLInputElement | HTMLTextAreaElement) {
      const errorElement = document.getElementById(`${field.id}-error`) as HTMLElement;
      field.classList.remove('error');
      field.setAttribute('aria-invalid', 'false');
      if (errorElement) {
        errorElement.textContent = '';
        errorElement.classList.add('hidden');
      }
    }

    showError(field: HTMLInputElement | HTMLTextAreaElement, message: string) {
      const errorElement = document.getElementById(`${field.id}-error`) as HTMLElement;
      field.classList.add('error');
      field.setAttribute('aria-invalid', 'true');
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
      }
    }

    getFieldLabel(field: HTMLInputElement | HTMLTextAreaElement): string {
      const label = this.form.querySelector(`label[for="${field.id}"]`) as HTMLLabelElement;
      return label ? label.textContent?.replace('*', '').trim() || field.name : field.name;
    }

    async handleSubmit(e: Event) {
      e.preventDefault();

      if (this.isSubmitting) return;

      // Validate all fields
      const inputs = this.form.querySelectorAll('input, textarea') as NodeListOf<HTMLInputElement | HTMLTextAreaElement>;
      let isFormValid = true;

      inputs.forEach(input => {
        if (!this.validateField(input)) {
          isFormValid = false;
        }
      });

      if (!isFormValid) {
        this.announceToScreenReader('Please correct the errors in the form.');
        return;
      }

      // Show loading state
      this.setSubmittingState(true);

      try {
        // Get form values
        const formData = new FormData(this.form);
        const data = {
          name: formData.get('name') as string,
          email: formData.get('email') as string,
          subject: formData.get('subject') as string,
          message: formData.get('message') as string
        };

        // Submit to PocketBase via API endpoint
        const response = await this.submitToAPI(data);

        if (response.success) {
          // Show success message
          this.showSuccessMessage(response.message);

          // Reset form
          this.form.reset();
          this.announceToScreenReader('Your message has been sent successfully. We will get back to you soon.');
        } else {
          throw new Error(response.error || 'Submission failed');
        }

      } catch (error) {
        console.error('Form submission error:', error);
        const errorMessage = error instanceof Error ? error.message : 'There was an error sending your message. Please try again.';
        this.showErrorMessage(errorMessage);
      } finally {
        this.setSubmittingState(false);
      }
    }

    async submitToAPI(data: any): Promise<{success: boolean, message?: string, error?: string, id?: string}> {
      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        console.error('API submission error:', error);
        throw error;
      }
    }

    setSubmittingState(isSubmitting: boolean) {
      this.isSubmitting = isSubmitting;

      if (isSubmitting) {
        this.submitBtn.disabled = true;
        this.submitBtn.innerHTML = `
          <span class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          </span>
        `;
      } else {
        this.submitBtn.disabled = false;
        this.submitBtn.innerHTML = `
          <span class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 transition-transform duration-300 group-hover:translate-x-1">
              <path d="m22 2-7 20-4-9-9-4Z"></path>
              <path d="M22 2 11 13"></path>
            </svg>
            Send Message
          </span>
        `;
      }
    }

    showSuccessMessage(customMessage?: string) {
      // Create and show success notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 max-w-md';
      notification.innerHTML = `
        <div class="flex items-start">
          <svg class="w-6 h-6 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <div>
            <div class="font-semibold">Success!</div>
            <div class="text-sm opacity-90">${customMessage || 'Your message has been sent successfully!'}</div>
          </div>
          <button class="ml-4 text-white hover:text-gray-200 transition-colors" onclick="this.parentElement.parentElement.remove()">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      `;

      document.body.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // Remove after 7 seconds
      setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
          if (notification.parentElement) {
            document.body.removeChild(notification);
          }
        }, 300);
      }, 7000);
    }

    showErrorMessage(message: string) {
      // Create and show error notification
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 max-w-md';
      notification.innerHTML = `
        <div class="flex items-start">
          <svg class="w-6 h-6 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <div>
            <div class="font-semibold">Error</div>
            <div class="text-sm opacity-90">${message}</div>
          </div>
          <button class="ml-4 text-white hover:text-gray-200 transition-colors" onclick="this.parentElement.parentElement.remove()">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      `;

      document.body.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // Remove after 7 seconds
      setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
          if (notification.parentElement) {
            document.body.removeChild(notification);
          }
        }, 300);
      }, 7000);
    }

    announceToScreenReader(message: string) {
      const liveRegion = document.getElementById('form-feedback') as HTMLElement;
      if (liveRegion) {
        liveRegion.textContent = message;
        setTimeout(() => {
          liveRegion.textContent = '';
        }, 1000);
      }
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ContactFormController();
  });
</script>

<style>
  /* Enhanced Contact Page Styles */

  /* Premium card effects */
  .premium-card {
    transform-style: preserve-3d;
    perspective: 1000px;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0.85)
    );
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
  }

  .premium-card:hover {
    transform: translateY(-8px) rotateX(2deg) rotateY(2deg);
    box-shadow:
      0 25px 50px -12px rgba(0, 56, 32, 0.25),
      0 0 0 1px rgba(0, 56, 32, 0.1);
  }

  .card-border {
    background: linear-gradient(115deg,
      #003820 0%,
      #184E35 25%,
      #005732 50%,
      #184E35 75%,
      #003820 100%
    );
    background-size: 300% 300%;
    animation: gradient-shift 8s ease infinite;
    opacity: 0.15;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: blur(12px);
    transform: scale(1.05);
  }

  .premium-card:hover .card-border {
    opacity: 0.3;
    animation-duration: 4s;
    filter: blur(8px);
    transform: scale(1.02);
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Contact item animations */
  .contact-item {
    transition: all 0.3s ease;
    border-radius: 1rem;
    padding: 1rem;
    margin: -1rem;
  }

  .contact-item:hover {
    background: rgba(0, 56, 32, 0.03);
    transform: translateX(8px);
  }

  .contact-icon-wrapper {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, rgba(0, 56, 32, 0.1), rgba(0, 87, 50, 0.05));
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .contact-item:hover .contact-icon-wrapper {
    background: linear-gradient(135deg, #003820, #005732);
    transform: scale(1.1) rotate(5deg);
  }

  .contact-icon {
    color: #003820;
    transition: all 0.3s ease;
  }

  .contact-item:hover .contact-icon {
    color: white;
  }

  /* Social links */
  .social-link {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, rgba(0, 56, 32, 0.1), rgba(0, 87, 50, 0.05));
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #003820;
    position: relative;
    overflow: hidden;
  }

  .social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  .social-link:hover::before {
    left: 100%;
  }

  .social-link:hover {
    background: linear-gradient(135deg, #003820, #005732);
    color: white;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 56, 32, 0.3);
  }

  /* Form styles */
  .form-group {
    position: relative;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: #003820;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  .form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(0, 56, 32, 0.1);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #1f2937;
    font-size: 1rem;
    transition: all 0.3s ease;
    outline: none;
  }

  .form-input:focus {
    border-color: #003820;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(0, 56, 32, 0.1);
    transform: translateY(-1px);
  }

  .form-input::placeholder {
    color: rgba(60, 65, 69, 0.6);
  }

  .form-input.error {
    border-color: #ef4444;
    background: rgba(254, 242, 242, 0.8);
  }

  .error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
  }

  .error-message::before {
    content: '⚠';
    margin-right: 0.5rem;
  }

  /* Submit button */
  .submit-button {
    background: linear-gradient(135deg, #003820, #005732);
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 160px;
  }

  .submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .submit-button:hover::before {
    left: 100%;
  }

  .submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 56, 32, 0.4);
    background: linear-gradient(135deg, #004d2e, #006b3e);
  }

  .submit-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .submit-button:disabled:hover {
    transform: none;
    box-shadow: none;
  }

  /* Hover lift effect */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  /* Background animations */
  .bg-grid {
    background-image: linear-gradient(rgba(0, 56, 32, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 56, 32, 0.05) 1px, transparent 1px);
    background-size: 32px 32px;
    background-position: center center;
  }

  /* Floating animation keyframes */
  @keyframes float-slow {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(2deg); }
  }

  @keyframes float-reverse {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(15px) rotate(-1deg); }
  }

  .animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
  }

  .animate-float-reverse {
    animation: float-reverse 10s ease-in-out infinite;
  }

  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Responsive improvements */
  @media (max-width: 768px) {
    .premium-card:hover {
      transform: translateY(-4px);
    }

    .contact-item:hover {
      transform: translateX(4px);
    }

    .form-input {
      padding: 0.875rem 1rem;
    }

    .submit-button {
      width: 100%;
      max-width: 300px;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .form-input {
      border-width: 3px;
    }

    .premium-card {
      border: 2px solid #003820;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .premium-card,
    .contact-item,
    .social-link,
    .form-input,
    .submit-button,
    .hover-lift {
      transition: none;
      animation: none;
    }

    .animate-float-slow,
    .animate-float-reverse {
      animation: none;
    }
  }
</style>