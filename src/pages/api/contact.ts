import type { APIRoute } from 'astro';
import type { TypedPocketBase } from '@data/pocketbase-types';
import PocketBase from 'pocketbase';

export const POST: APIRoute = async ({ request }) => {
  try {
    // Initialize PocketBase
    const pb = new PocketBase(
      import.meta.env.POCKETBASE_URL || process.env.POCKETBASE_URL
    ) as TypedPocketBase;

    // Parse the request body
    const formData = await request.json();
    
    // Validate required fields
    const { name, email, subject, message } = formData;
    
    if (!name || !email || !subject || !message) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'All fields are required' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid email format' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get client IP and user agent for tracking
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Prepare data for PocketBase
    const enquiryData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      subject: subject.trim(),
      message: message.trim(),
      status: 'new',
      source: 'contact_form',
      ip_address: clientIP,
      user_agent: userAgent
    };

    // Create the enquiry record in PocketBase
    const record = await pb.collection('enquiries').create(enquiryData);

    // Log successful submission
    console.log('Contact form submission created:', record.id);

    // Return success response
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Your enquiry has been submitted successfully. We will get back to you within 24 hours.',
        id: record.id
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    // Return error response
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'There was an error processing your request. Please try again later.' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle preflight requests for CORS
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
