---
interface Props {
    class?: string;
}

const { class: className } = Astro.props;
import '../styles/animations.css';
import { getRecentBlogPosts, BlogServiceError } from '@lib/blog-service.ts';
import { transformBlogPostsToAstroContent, createFallbackBlogPost, formatBlogDate } from '@lib/blog-adapter.ts';

// Fetch blog posts from PocketBase with error handling
let blogPosts;
let hasError = false;
let errorMessage = '';

try {
  const recentPosts = await getRecentBlogPosts(3);
  blogPosts = transformBlogPostsToAstroContent(recentPosts);
} catch (error) {
  hasError = true;
  if (error instanceof BlogServiceError) {
    errorMessage = error.message;
    console.error('Blog service error:', error);
  } else {
    errorMessage = 'Failed to load blog posts';
    console.error('Unexpected error loading blog posts:', error);
  }

  // Provide fallback posts to maintain layout
  blogPosts = [
    createFallbackBlogPost('fallback-1'),
    createFallbackBlogPost('fallback-2'),
    createFallbackBlogPost('fallback-3')
  ];
}

const formattedDate = (date: string) => formatBlogDate(date);
---

<section class:list={["w-full blog-section", className]}>
    <div class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
        <div class="flex flex-col gap-8 md:gap-12 lg:gap-16">
            <div class="flex flex-col items-center text-center space-y-6 fade-in">
                <div class="inline-flex items-center bg-[#003820]/5 shadow-[rgba(0,0,0,0.08)_0_0_1px_1px,rgba(255,255,255,0.25)_4px_4px_4px_inset] text-[#003820] px-4 py-2 rounded-full hover:scale-105 transition-transform duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" class="w-4 h-4 mr-2">
                        <path d="M2.5 2.5C2.5 1.67157 3.17157 1 4 1H12C12.8284 1 13.5 1.67157 13.5 2.5V13.5C13.5 14.3284 12.8284 15 12 15H4C3.17157 15 2.5 14.3284 2.5 13.5V2.5Z" stroke="currentColor" stroke-width="1.3"/>
                        <path d="M5 4.5H11" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
                        <path d="M5 7.5H11" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
                        <path d="M5 10.5H9" stroke="currentColor" stroke-width="1.3" stroke-linecap="round"/>
                    </svg>
                    <span class="text-sm">Blog</span>
                </div>
                <h2 class="text-3xl md:text-5xl lg:text-6xl font-normal tracking-tight">
                    Expert Insights on<br/><span class="text-[#003820]">Litigation Funding</span>
                </h2>
            </div>

            {hasError && (
                <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                        <p class="text-yellow-800 text-sm">
                            <strong>Notice:</strong> {errorMessage}. Showing fallback content.
                        </p>
                    </div>
                </div>
            )}

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {blogPosts.map((post) => (
                    <div class="group slide-in-right">
                        <a href={`/blog/${post.slug}/`} class="flex flex-col h-full overflow-hidden bg-white rounded-3xl border border-[#003820]/10 hover:border-[#003820]/30 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                            <div class="relative aspect-[16/12] overflow-hidden">
                                {post.data.metaImage ? (
                                    <img
                                        src={post.data.metaImage}
                                        loading="lazy"
                                        alt={post.data.title}
                                        class="w-full h-full object-cover"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"
                                    />
                                ) : null}
                                <div
                                    class={`w-full h-full bg-gradient-to-br from-[#003820]/10 to-[#003820]/20 flex items-center justify-center ${post.data.metaImage ? 'hidden' : 'flex'}`}
                                >
                                    <div class="text-center text-[#003820]/60">
                                        <svg class="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                        </svg>
                                        <p class="text-sm font-medium">Blog Post</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col flex-grow p-6 space-y-4">
                                <div class="flex flex-wrap gap-2">
                                    <div class="inline-flex items-center bg-[#003820]/5 px-3 py-1 rounded-full">
                                        <span class="text-xs text-[#003820]">{formattedDate(post.data.datePublished)}</span>
                                    </div>
                                </div>
                                <h3 class="text-xl font-medium text-gray-900 group-hover:text-[#003820] transition-colors">
                                    {post.data.title}
                                </h3>
                                <p class="text-[#3C4145] text-sm flex-grow">
                                    {post.data.summary}
                                </p>
                                <div class="inline-flex items-center text-[#003820] font-medium">
                                    Read More
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4 ml-1">
                                        <path fill-rule="evenodd" d="M3 10a.75.75 0 01.75-.75h10.638L10.23 5.29a.75.75 0 111.04-1.08l5.5 5.25a.75.75 0 010 1.08l-5.5 5.25a.75.75 0 11-1.04-1.08l4.158-3.96H3.75A.75.75 0 013 10z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </a>
                    </div>
                ))}
            </div>

            <div class="flex justify-center scale-in">
                <a href="/blog" class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-[#003820] to-[#005732] rounded-full hover:opacity-90 transition-opacity">
                    View all articles
                </a>
            </div>
        </div>
    </div>
</section>