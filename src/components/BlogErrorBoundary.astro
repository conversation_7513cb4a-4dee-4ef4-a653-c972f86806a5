---
interface Props {
  error?: string;
  type?: 'warning' | 'error' | 'info';
  showRetry?: boolean;
  retryUrl?: string;
}

const {
  error,
  type = 'warning',
  showRetry = false,
  retryUrl
} = Astro.props;

const iconMap = {
  warning: {
    class: 'text-yellow-600',
    path: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z'
  },
  error: {
    class: 'text-red-600',
    path: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z'
  },
  info: {
    class: 'text-blue-600',
    path: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
  }
};

const colorMap = {
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800'
};

const icon = iconMap[type];
const colors = colorMap[type];
---

{error && (
  <div class={`mb-6 p-4 border rounded-lg ${colors}`}>
    <div class="flex items-start">
      <svg class={`w-5 h-5 mr-3 mt-0.5 flex-shrink-0 ${icon.class}`} fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d={icon.path} clip-rule="evenodd" />
      </svg>
      <div class="flex-1">
        <p class="text-sm">
          <strong>{type === 'error' ? 'Error:' : type === 'warning' ? 'Notice:' : 'Info:'}</strong>
          {error}
        </p>
        {showRetry && retryUrl && (
          <div class="mt-3">
            <a
              href={retryUrl}
              class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md bg-white border border-gray-300 hover:bg-gray-50 transition-colors"
            >
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Retry
            </a>
          </div>
        )}
      </div>
    </div>
  </div>
)}