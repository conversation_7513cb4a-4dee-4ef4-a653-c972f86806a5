---
interface Props {
  type?: 'grid' | 'single' | 'list';
  count?: number;
}

const { type = 'grid', count = 3 } = Astro.props;
---

{type === 'grid' && (
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: count }).map(() => (
      <div class="animate-pulse">
        <div class="flex flex-col h-full overflow-hidden bg-white rounded-3xl border border-gray-200">
          <div class="relative aspect-[16/12] overflow-hidden">
            <div class="w-full h-full bg-gray-200"></div>
          </div>
          <div class="flex flex-col flex-grow p-6 space-y-4">
            <div class="flex flex-wrap gap-2">
              <div class="bg-gray-200 px-3 py-1 rounded-full w-20 h-6"></div>
            </div>
            <div class="space-y-2">
              <div class="h-6 bg-gray-200 rounded w-3/4"></div>
              <div class="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded"></div>
              <div class="h-4 bg-gray-200 rounded w-5/6"></div>
              <div class="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
            <div class="h-6 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
)}

{type === 'single' && (
  <div class="animate-pulse">
    <div class="space-y-6">
      <div class="h-8 bg-gray-200 rounded w-3/4 mx-auto"></div>
      <div class="h-4 bg-gray-200 rounded w-1/4 mx-auto"></div>
      <div class="aspect-[16/9] bg-gray-200 rounded-xl"></div>
      <div class="space-y-4">
        <div class="h-4 bg-gray-200 rounded"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
        <div class="h-4 bg-gray-200 rounded w-4/5"></div>
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
        <div class="h-4 bg-gray-200 rounded w-2/3"></div>
      </div>
    </div>
  </div>
)}

{type === 'list' && (
  <div class="space-y-4">
    {Array.from({ length: count }).map(() => (
      <div class="animate-pulse flex space-x-4 p-4 border border-gray-200 rounded-lg">
        <div class="w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0"></div>
        <div class="flex-1 space-y-2">
          <div class="h-5 bg-gray-200 rounded w-3/4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/4"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    ))}
  </div>
)}

<style>
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
</style>