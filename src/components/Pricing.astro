---
const pricingPlans = [
	{ title: 'Basic Plan', price: '$99.00', period: '/month', features: ['Unlimited Invoices', 'Next-Day Payments', 'Secure Payment Gateway', 'Basic Analytics and Reporting', 'Email Support'] },
	{ title: 'Pro Plan', price: '$159.00', period: '/month', features: ['Unlimited Invoices', 'Next-Day Payments', 'Secure Payment Gateway', 'Advanced Analytics and Reporting', 'Priority Email Support', 'Phone Support'] },
	{ title: 'Enterprise Plan', price: '$399.00', period: '/month', features: ['Unlimited Invoices', 'Next-Day Payments', 'Secure Payment Gateway', 'Advanced Analytics and Reporting', 'Dedicated Account Manager', '24/7 Phone and Email Support'] }
];
---

<section class="section_pricing">
	<div class="padding-global padding-section-medium">
		<div class="container-large">
			<div class="pricing_wrap">
				<div class="pricing_title-wrap">
					<div class="pricing_title">
						<div class="tag">
							<svg xmlns="http://www.w3.org/2000/svg" width="100%" viewBox="0 0 17 16" fill="none" class="icon-1x1-small">
								<path d="M8.39844 4.15039C8.39844 6.22146 6.7195 7.90039 4.64844 7.90039H0.898438V4.15039C0.898438 2.07932 2.57737 0.400391 4.64844 0.400391C6.7195 0.400391 8.39844 2.07932 8.39844 4.15039Z" fill="currentColor"/>
								<path d="M8.39844 11.6504C8.39844 9.57932 10.0774 7.90039 12.1484 7.90039H15.8984V11.6504C15.8984 13.7215 14.2195 15.4004 12.1484 15.4004C10.0774 15.4004 8.39844 13.7215 8.39844 11.6504Z" fill="currentColor"/>
								<path d="M0.898438 11.6504C0.898438 13.7215 2.57737 15.4004 4.64844 15.4004H8.39844V11.6504C8.39844 9.57932 6.7195 7.90039 4.64844 7.90039C2.57737 7.90039 0.898438 9.57932 0.898438 11.6504Z" fill="currentColor"/>
								<path d="M15.8984 4.15039C15.8984 2.07932 14.2195 0.400391 12.1484 0.400391H8.39844V4.15039C8.39844 6.22146 10.0774 7.90039 12.1484 7.90039C14.2195 7.90039 15.8984 6.22146 15.8984 4.15039Z" fill="currentColor"/>
							</svg>
							<div class="text-sm">Pricing</div>
						</div>
						<h2 class="text-6xl text-align-center">Simple and transparant <span class="text-color-tertiary">pricing</span></h2>
					</div>
					<div class="pricing_text-wrap text-align-center">
						<div class="text-base text-color-secondary">Choose a plan that fits your business needs and budget. No hidden fees, no surprises, just straight forward pricing for powerful financial management.</div>
					</div>
				</div>
				<div class="tabs_component w-tabs">
					<div class="tabs_menu w-tab-menu" role="tablist">
						<a data-w-tab="Tab 1" class="tab_link w-inline-block w-tab-link" tabindex="-1" id="w-tabs-0-data-w-tab-0" href="#w-tabs-0-data-w-pane-0" role="tab" aria-controls="w-tabs-0-data-w-pane-0" aria-selected="false">
							<div class="text-base">Monthly</div>
						</a>
						<a data-w-tab="Tab 2" class="tab_link w-inline-block w-tab-link w--current" id="w-tabs-0-data-w-tab-1" href="#w-tabs-0-data-w-pane-1" role="tab" aria-controls="w-tabs-0-data-w-pane-1" aria-selected="true">
							<div class="text-base">Yearly</div>
						</a>
					</div>
					<div class="tabs_content w-tab-content">
						<div data-w-tab="Tab 1" class="tab_pane w-tab-pane" id="w-tabs-0-data-w-pane-0" role="tabpanel" aria-labelledby="w-tabs-0-data-w-tab-0">
							<div class="tab_grid">
								{pricingPlans.map((plan) => (
									<div class="block">
										<div class="text-2xl">{plan.title}</div>
										<div class="card_details">
											<div class="card_price">
												<div class="text-3xl">{plan.price}</div>
												<div class="text-sm text-color-secondary">{plan.period}</div>
											</div>
											<div class="text-base text-color-secondary">Perfect for small businesses or startups, our Starter Plan gives you the essential tools to manage your finances with ease</div>
										</div>
										<a href="https://www.temlis.com/" class="button is-secondary w-button">Get Started</a>
										<ul role="list" class="card_list w-list-unstyled">
											{plan.features.map((feature) => (
												<li class="list-item">
													<img src="/images/66ecaa037c28588af9bd00f9_tick-circle.svg" loading="lazy" alt="" class="icon-1x1-small" />
													<div class="text-sm text-color-primary">{feature}</div>
												</li>
											))}
										</ul>
									</div>
								))}
							</div>
						</div>
						<div data-w-tab="Tab 2" class="tab_pane w-tab-pane w--tab-active" id="w-tabs-0-data-w-pane-1" role="tabpanel" aria-labelledby="w-tabs-0-data-w-tab-1">
							<div class="tab_grid">
								{pricingPlans.map((plan) => (
									<div class="block">
										<div class="text-2xl">{plan.title}</div>
										<div class="card_details">
											<div class="card_price">
												<div class="text-3xl">${plan.price * 12}</div>
												<div class="text-sm text-color-secondary">/ year</div>
											</div>
											<div class="text-base text-color-secondary">Perfect for small businesses or startups, our Starter Plan gives you the essential tools to manage your finances with ease</div>
										</div>
										<a href="https://www.temlis.com/" class="button is-secondary w-button">Get Started</a>
										<ul role="list" class="card_list w-list-unstyled">
											{plan.features.map((feature) => (
												<li class="list-item">
													<img src="/images/66ecaa037c28588af9bd00f9_tick-circle.svg" loading="lazy" alt="" class="icon-1x1-small" />
													<div class="text-sm text-color-primary">{feature}</div>
												</li>
											))}
										</ul>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>