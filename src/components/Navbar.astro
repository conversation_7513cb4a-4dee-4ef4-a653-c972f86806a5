---
---

<nav class="fixed top-0 left-0 right-0 z-50 px-4 py-4" role="navigation" aria-label="Main navigation">
	<div class="max-w-7xl mx-auto">
		<div class="bg-white/85 backdrop-blur-xl border border-[#003820]/15 rounded-full px-6 py-3 flex items-center justify-between shadow-lg shadow-[#003820]/5">
			<a href="/" class="flex items-center group" aria-label="3Pay Global - Home">
				<img src="/images/3paylogo.png" alt="3Pay Global Logo" class="h-8 w-auto transition-transform duration-300 group-hover:scale-105" />
			</a>

			<div class="hidden md:flex items-center space-x-1">
				<a href="/solicitors" class="nav-link text-[#3C4145] hover:text-[#003820] transition-all duration-300 px-4 py-2 rounded-full relative overflow-hidden group" aria-current="page">
					<span class="relative z-10">Solicitors</span>
					<div class="absolute inset-0 bg-[#003820]/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full"></div>
				</a>
				<a href="/claimants" class="nav-link text-[#3C4145] hover:text-[#003820] transition-all duration-300 px-4 py-2 rounded-full relative overflow-hidden group">
					<span class="relative z-10">Claimants</span>
					<div class="absolute inset-0 bg-[#003820]/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full"></div>
				</a>
				<a href="/co-funders" class="nav-link text-[#3C4145] hover:text-[#003820] transition-all duration-300 px-4 py-2 rounded-full relative overflow-hidden group">
					<span class="relative z-10">Co-Funders</span>
					<div class="absolute inset-0 bg-[#003820]/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full"></div>
				</a>
				<a href="/blog" class="nav-link text-[#3C4145] hover:text-[#003820] transition-all duration-300 px-4 py-2 rounded-full relative overflow-hidden group">
					<span class="relative z-10">Blog</span>
					<div class="absolute inset-0 bg-[#003820]/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full"></div>
				</a>
				<div class="relative">
					<button
						class="company-button text-[#3C4145] text-base font-normal hover:text-[#003820] transition-all duration-300 flex items-center gap-2 px-4 py-2 rounded-full relative overflow-hidden group focus:outline-none focus:ring-2 focus:ring-[#003820]/20 focus:ring-offset-2"
						aria-expanded="false"
						aria-haspopup="true"
						aria-controls="company-submenu"
						id="company-menu-button"
					>
						<span class="relative z-10">Company</span>
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="dropdown-arrow transition-transform duration-300">
							<path d="m6 9 6 6 6-6"/>
						</svg>
						<div class="absolute inset-0 bg-[#003820]/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-full"></div>
					</button>
					<div
						id="company-submenu"
						class="absolute hidden top-[calc(100%+8px)] left-0 bg-white/95 backdrop-blur-lg rounded-xl shadow-xl border border-[#003820]/10 py-2 w-52 transition-all duration-300 opacity-0 translate-y-2 pointer-events-none"
						role="menu"
						aria-labelledby="company-menu-button"
					>
						<div class="absolute -top-1 left-6 w-2 h-2 bg-white/95 border-l border-t border-[#003820]/10 rotate-45"></div>
						<a href="/about" class="block px-4 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-200 rounded-lg mx-2 group" role="menuitem">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-3 opacity-60 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
								</svg>
								About
							</span>
						</a>
						<a href="/help" class="block px-4 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-200 rounded-lg mx-2 group" role="menuitem">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-3 opacity-60 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
								</svg>
								Help
							</span>
						</a>
						<a href="/contact" class="block px-4 py-3 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-200 rounded-lg mx-2 group" role="menuitem">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-3 opacity-60 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
								</svg>
								Contact
							</span>
						</a>
					</div>
				</div>
			</div>

			<div class="flex items-center gap-4">
				<a
					href="https://app.3payglobal.com/#/signin"
					target="_blank"
					rel="noopener noreferrer"
					class="bg-gradient-to-r from-[#003820] to-[#005732] text-white px-6 py-2.5 rounded-full hover:from-[#004d2e] hover:to-[#006b3e] transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg hover:shadow-[#003820]/25 focus:outline-none focus:ring-2 focus:ring-[#003820]/20 focus:ring-offset-2 font-medium"
					aria-label="Apply now - Opens in new window"
				>
					Apply Now
				</a>
				<button
					class="md:hidden flex flex-col items-center justify-center w-11 h-11 bg-[#003820] rounded-xl border border-white/20 hover:bg-[#004d2e] transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#003820]/20 focus:ring-offset-2"
					id="mobile-menu-button"
					aria-label="Toggle mobile menu"
					aria-expanded="false"
					aria-controls="mobile-menu"
				>
					<span class="w-5 h-0.5 bg-white rounded-sm transition-all duration-300 nav-button_line is-first"></span>
					<span class="w-5 h-0.5 bg-white rounded-sm transition-all duration-300 mt-1.5 nav-button_line is-second"></span>
					<span class="w-5 h-0.5 bg-white rounded-sm transition-all duration-300 mt-1.5 nav-button_line is-third"></span>
				</button>
			</div>
		</div>
	</div>
</nav>

<div
	id="mobile-menu"
	class="fixed top-[5rem] left-4 right-4 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl shadow-[#003820]/10 py-6 w-auto md:hidden transition-all duration-500 ease-out z-[60] opacity-0 -translate-y-6 scale-95 invisible border border-[#003820]/15"
	role="menu"
	aria-labelledby="mobile-menu-button"
>
	<div class="flex flex-col space-y-1 px-2">
		<a href="/solicitors" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
			</svg>
			<span class="text-base font-medium">Solicitors</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>
		<a href="/claimants" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
			</svg>
			<span class="text-base font-medium">Claimants</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>
		<a href="/co-funders" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
			</svg>
			<span class="text-base font-medium">Co-Funders</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>
		<a href="/blog" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
			</svg>
			<span class="text-base font-medium">Blog</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>

		<div class="h-px bg-gradient-to-r from-transparent via-[#003820]/20 to-transparent my-4 mx-4"></div>

		<div class="px-2 py-1">
			<p class="text-xs font-medium text-[#003820]/60 uppercase tracking-wider mb-2 px-2">Company</p>
		</div>

		<a href="/about" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
			</svg>
			<span class="text-base font-medium">About</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>
		<a href="/help" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
			</svg>
			<span class="text-base font-medium">Help</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>
		<a href="/contact" class="mobile-nav-link flex items-center px-4 py-3.5 text-[#3C4145] hover:text-[#003820] hover:bg-[#003820]/8 transition-all duration-300 rounded-xl group" role="menuitem">
			<svg class="w-5 h-5 mr-4 opacity-60 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
			</svg>
			<span class="text-base font-medium">Contact</span>
			<svg class="w-4 h-4 ml-auto opacity-0 group-hover:opacity-60 transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
			</svg>
		</a>

		<div class="pt-4 px-2">
			<a
				href="https://app.3payglobal.com/#/signin"
				target="_blank"
				rel="noopener noreferrer"
				class="flex items-center justify-center w-full px-6 py-3.5 bg-gradient-to-r from-[#003820] to-[#005732] text-white rounded-xl hover:from-[#004d2e] hover:to-[#006b3e] transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg hover:shadow-[#003820]/25 font-medium"
				role="menuitem"
			>
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
				</svg>
				Apply Now
			</a>
		</div>
	</div>
</div>

<style>
	/* Enhanced navigation styles */
	.nav-link {
		position: relative;
		font-weight: 500;
		letter-spacing: -0.01em;
	}

	.nav-link:focus {
		outline: 2px solid #003820;
		outline-offset: 2px;
		border-radius: 0.5rem;
	}

	/* Active page indicator */
	.nav-link[aria-current="page"] {
		color: #003820;
		font-weight: 600;
	}

	.nav-link[aria-current="page"]::after {
		content: '';
		position: absolute;
		bottom: -0.5rem;
		left: 50%;
		transform: translateX(-50%);
		width: 6px;
		height: 6px;
		background: #003820;
		border-radius: 50%;
	}

	/* Company dropdown enhancements */
	.company-button {
		position: relative;
		font-weight: 500;
		letter-spacing: -0.01em;
	}

	.company-button[aria-expanded="true"] .dropdown-arrow {
		transform: rotate(180deg);
	}

	.company-button[aria-expanded="true"] + #company-submenu {
		opacity: 1;
		transform: translateY(0);
		pointer-events: auto;
		visibility: visible;
	}

	/* Mobile menu button animations */
	.nav-button_line {
		transform-origin: center;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.mobile-menu-open .is-first {
		transform: translateY(6px) rotate(45deg);
	}

	.mobile-menu-open .is-second {
		opacity: 0;
		transform: scale(0);
	}

	.mobile-menu-open .is-third {
		transform: translateY(-6px) rotate(-45deg);
	}

	/* Mobile menu animations */
	.mobile-menu-open + #mobile-menu {
		opacity: 1;
		transform: translateY(0) scale(1);
		visibility: visible;
	}

	/* Mobile nav link animations */
	.mobile-nav-link {
		transform: translateX(-10px);
		opacity: 0;
		animation: slideInLeft 0.3s ease-out forwards;
	}

	.mobile-nav-link:nth-child(1) { animation-delay: 0.1s; }
	.mobile-nav-link:nth-child(2) { animation-delay: 0.15s; }
	.mobile-nav-link:nth-child(3) { animation-delay: 0.2s; }
	.mobile-nav-link:nth-child(4) { animation-delay: 0.25s; }
	.mobile-nav-link:nth-child(6) { animation-delay: 0.3s; }
	.mobile-nav-link:nth-child(7) { animation-delay: 0.35s; }
	.mobile-nav-link:nth-child(8) { animation-delay: 0.4s; }

	@keyframes slideInLeft {
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	/* Focus management for accessibility */
	.nav-link:focus-visible,
	.company-button:focus-visible,
	.mobile-nav-link:focus-visible {
		outline: 2px solid #003820;
		outline-offset: 2px;
		border-radius: 0.5rem;
	}

	/* Smooth backdrop blur enhancement */
	@supports (backdrop-filter: blur(20px)) {
		nav > div > div {
			backdrop-filter: blur(20px);
		}

		#mobile-menu {
			backdrop-filter: blur(20px);
		}

		#company-submenu {
			backdrop-filter: blur(16px);
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.nav-button_line,
		.dropdown-arrow,
		.mobile-nav-link,
		#mobile-menu,
		#company-submenu {
			transition: none;
			animation: none;
		}
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.nav-link,
		.company-button,
		.mobile-nav-link {
			border: 1px solid transparent;
		}

		.nav-link:hover,
		.company-button:hover,
		.mobile-nav-link:hover {
			border-color: currentColor;
		}
	}
</style>


<script>
	class NavbarController {
		constructor() {
			this.companyButton = document.querySelector('.company-button');
			this.companySubmenu = document.getElementById('company-submenu');
			this.mobileMenuButton = document.getElementById('mobile-menu-button');
			this.mobileMenu = document.getElementById('mobile-menu');
			this.isMenuOpen = false;
			this.isDropdownOpen = false;

			this.init();
		}

		init() {
			this.setupEventListeners();
			this.setupKeyboardNavigation();
			this.setActivePageIndicator();
		}

		setupEventListeners() {
			// Company dropdown
			this.companyButton?.addEventListener('click', (e) => {
				e.preventDefault();
				this.toggleDropdown();
			});

			// Close dropdown when clicking outside
			document.addEventListener('click', (event) => {
				if (!this.companyButton?.contains(event.target) &&
					!this.companySubmenu?.contains(event.target)) {
					this.closeDropdown();
				}
			});

			// Mobile menu
			this.mobileMenuButton?.addEventListener('click', (e) => {
				e.preventDefault();
				this.toggleMobileMenu();
			});

			// Close mobile menu when clicking on links
			this.mobileMenu?.querySelectorAll('a').forEach(link => {
				link.addEventListener('click', () => {
					this.closeMobileMenu();
				});
			});

			// Handle escape key
			document.addEventListener('keydown', (e) => {
				if (e.key === 'Escape') {
					this.closeDropdown();
					this.closeMobileMenu();
				}
			});

			// Handle resize
			window.addEventListener('resize', () => {
				if (window.innerWidth >= 768 && this.isMenuOpen) {
					this.closeMobileMenu();
				}
			});
		}

		setupKeyboardNavigation() {
			// Arrow key navigation for dropdown
			this.companySubmenu?.addEventListener('keydown', (e) => {
				const menuItems = this.companySubmenu.querySelectorAll('a');
				const currentIndex = Array.from(menuItems).indexOf(document.activeElement);

				switch (e.key) {
					case 'ArrowDown':
						e.preventDefault();
						const nextIndex = (currentIndex + 1) % menuItems.length;
						menuItems[nextIndex]?.focus();
						break;
					case 'ArrowUp':
						e.preventDefault();
						const prevIndex = currentIndex === 0 ? menuItems.length - 1 : currentIndex - 1;
						menuItems[prevIndex]?.focus();
						break;
					case 'Home':
						e.preventDefault();
						menuItems[0]?.focus();
						break;
					case 'End':
						e.preventDefault();
						menuItems[menuItems.length - 1]?.focus();
						break;
				}
			});
		}

		setActivePageIndicator() {
			const currentPath = window.location.pathname;
			const navLinks = document.querySelectorAll('.nav-link');

			navLinks.forEach(link => {
				const href = link.getAttribute('href');
				if (href === currentPath || (currentPath !== '/' && href !== '/' && currentPath.startsWith(href))) {
					link.setAttribute('aria-current', 'page');
				}
			});
		}

		toggleDropdown() {
			this.isDropdownOpen = !this.isDropdownOpen;
			this.companyButton?.setAttribute('aria-expanded', this.isDropdownOpen.toString());

			if (this.isDropdownOpen) {
				this.companySubmenu?.classList.remove('hidden');
				// Focus first menu item
				setTimeout(() => {
					this.companySubmenu?.querySelector('a')?.focus();
				}, 100);
			} else {
				this.closeDropdown();
			}
		}

		closeDropdown() {
			this.isDropdownOpen = false;
			this.companyButton?.setAttribute('aria-expanded', 'false');
			this.companySubmenu?.classList.add('hidden');
		}

		toggleMobileMenu() {
			this.isMenuOpen = !this.isMenuOpen;
			this.mobileMenuButton?.setAttribute('aria-expanded', this.isMenuOpen.toString());
			this.mobileMenuButton?.classList.toggle('mobile-menu-open');

			if (this.isMenuOpen) {
				this.openMobileMenu();
			} else {
				this.closeMobileMenu();
			}
		}

		openMobileMenu() {
			this.mobileMenu?.classList.remove('invisible');
			// Prevent body scroll
			document.body.style.overflow = 'hidden';

			// Trigger animation
			requestAnimationFrame(() => {
				this.mobileMenu?.classList.remove('opacity-0', '-translate-y-6', 'scale-95');
			});
		}

		closeMobileMenu() {
			this.isMenuOpen = false;
			this.mobileMenuButton?.setAttribute('aria-expanded', 'false');
			this.mobileMenuButton?.classList.remove('mobile-menu-open');

			// Restore body scroll
			document.body.style.overflow = '';

			// Trigger animation
			this.mobileMenu?.classList.add('opacity-0', '-translate-y-6', 'scale-95');

			// Hide after animation
			setTimeout(() => {
				this.mobileMenu?.classList.add('invisible');
			}, 500);
		}
	}

	// Initialize when DOM is loaded
	document.addEventListener('DOMContentLoaded', () => {
		new NavbarController();
	});
</script>
