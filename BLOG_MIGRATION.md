# Blog Migration Guide

This document outlines the migration from static Markdown files to dynamic PocketBase-powered blog functionality.

## Overview

The blog system has been migrated from Astro's static content collections to a dynamic system powered by PocketBase. This provides several benefits:

- **Dynamic Content Management**: Blog posts can be managed through PocketBase admin interface
- **Real-time Updates**: Content changes are reflected immediately without rebuilds
- **Enhanced Caching**: Intelligent caching system reduces database load
- **Better Error Handling**: Graceful fallbacks and error states
- **Scalability**: Database-backed system scales better than file-based approach

## Architecture

### Components

1. **Blog Service** (`src/lib/blog-service.ts`)
   - Handles all PocketBase interactions
   - Implements caching and error handling
   - Provides clean API for blog operations

2. **Blog Adapter** (`src/lib/blog-adapter.ts`)
   - Transforms PocketBase data to Astro-compatible format
   - Maintains backward compatibility with existing components
   - Handles error states and fallbacks

3. **Advanced Cache** (`src/lib/blog-cache.ts`)
   - LRU cache with TTL support
   - Optional localStorage persistence
   - Performance monitoring and statistics

4. **UI Components**
   - `BlogErrorBoundary.astro`: Error display component
   - `BlogLoadingState.astro`: Loading state component
   - Updated `Blog.astro` and `blog.astro` with PocketBase integration

### Database Schema

The `content_items` collection in PocketBase contains:

```typescript
interface ContentItem {
  id: string
  type: 'blog' | 'article' | 'news' | 'guide'
  title: string
  slug: string
  summary: string
  body_content: string // HTML/Markdown
  meta_image: string
  author: string
  meta_author: string
  date_published: string
  date_updated?: string
  previous_post?: string
  previous_post_title?: string
  next_post?: string
  next_post_title?: string
  status: 'draft' | 'published' | 'archived'
  published_at: string
  tags?: string[]
  analytics_views?: number
}
```

## Migration Process

### Prerequisites

1. **Environment Variables**
   ```bash
   POCKETBASE_URL=http://localhost:8090
   POCKETBASE_ADMIN_EMAIL=<EMAIL>
   POCKETBASE_ADMIN_PASSWORD=your_password
   ```

2. **Dependencies**
   ```bash
   npm install gray-matter tsx
   ```

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Run Migration Script

```bash
npm run migrate-blog
```

This script will:
- Read all `.md` files from `src/content/blog/`
- Parse frontmatter and content
- Create corresponding records in PocketBase
- Handle duplicates gracefully

### Step 3: Validate Migration

```bash
npm run test-blog
```

This will run comprehensive tests to ensure:
- All posts are accessible
- Content integrity is maintained
- Performance is acceptable
- Error handling works correctly
- URL structure is preserved

## Features

### Error Handling

The system includes robust error handling:

- **Network Failures**: Graceful fallbacks with user-friendly messages
- **Missing Content**: Fallback posts maintain layout integrity
- **Invalid Data**: Data validation and sanitization
- **Cache Failures**: Automatic cache invalidation and retry logic

### Performance Optimizations

- **Intelligent Caching**: 5-minute TTL with LRU eviction
- **Lazy Loading**: Images and content loaded on demand
- **Batch Operations**: Efficient database queries
- **CDN-Ready**: Static assets can be served from CDN

### SEO Preservation

- **URL Structure**: Maintains existing `/blog/[slug]/` pattern
- **Meta Tags**: All SEO metadata preserved
- **Sitemap**: Dynamic sitemap generation
- **Redirects**: Automatic handling of old URLs