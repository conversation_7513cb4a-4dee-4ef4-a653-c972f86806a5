#!/usr/bin/env node

/**
 * Test script to validate blog migration and functionality
 *
 * Usage: npm run test-blog
 *
 * This script validates:
 * 1. All blog posts are accessible via new PocketBase system
 * 2. URL structure is preserved
 * 3. Content integrity is maintained
 * 4. Error handling works correctly
 * 5. Performance is acceptable
 */

import { getAllBlogPosts, getBlogPostBySlug, BlogServiceError } from '../src/lib/blog-service.ts'
import { transformBlogPostsToAstroContent } from '../src/lib/blog-adapter.ts'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

interface TestResult {
  name: string
  passed: boolean
  message: string
  duration?: number
}

class BlogMigrationTester {
  private results: TestResult[] = []
  private originalPosts: any[] = []

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting blog migration tests...\n')

    // Load original posts for comparison
    await this.loadOriginalPosts()

    // Run test suites
    await this.testDataIntegrity()
    await this.testPerformance()
    await this.testErrorHandling()
    await this.testUrlStructure()
    await this.testContentTransformation()

    // Generate report
    this.generateReport()
  }

  private async loadOriginalPosts(): Promise<void> {
    const blogDir = path.join(process.cwd(), 'src/content/blog')
    const files = fs.readdirSync(blogDir).filter(file => file.endsWith('.md'))

    for (const file of files) {
      const filePath = path.join(blogDir, file)
      const fileContent = fs.readFileSync(filePath, 'utf-8')
      const { data: frontmatter, content } = matter(fileContent)

      this.originalPosts.push({
        slug: file.replace('.md', ''),
        frontmatter,
        content,
        filename: file
      })
    }

    console.log(`📁 Loaded ${this.originalPosts.length} original posts for comparison`)
  }

  private async testDataIntegrity(): Promise<void> {
    console.log('🔍 Testing data integrity...')

    // Test 1: All posts are accessible
    await this.runTest('All posts accessible', async () => {
      const posts = await getAllBlogPosts()
      if (posts.length === 0) {
        throw new Error('No posts returned from PocketBase')
      }
      return `Retrieved ${posts.length} posts`
    })

    // Test 2: Post count matches original
    await this.runTest('Post count matches original', async () => {
      const posts = await getAllBlogPosts()
      if (posts.length !== this.originalPosts.length) {
        throw new Error(`Expected ${this.originalPosts.length} posts, got ${posts.length}`)
      }
      return `Post count matches: ${posts.length}`
    })

    // Test 3: Individual post retrieval
    for (const originalPost of this.originalPosts.slice(0, 3)) { // Test first 3 posts
      await this.runTest(`Individual post retrieval: ${originalPost.slug}`, async () => {
        const post = await getBlogPostBySlug(originalPost.slug)
        if (!post) {
          throw new Error(`Post not found: ${originalPost.slug}`)
        }
        if (post.title !== originalPost.frontmatter.title) {
          throw new Error(`Title mismatch: expected "${originalPost.frontmatter.title}", got "${post.title}"`)
        }
        return `Post retrieved successfully`
      })
    }

    // Test 4: Content integrity
    await this.runTest('Content integrity check', async () => {
      const posts = await getAllBlogPosts()
      let checkedCount = 0

      for (const post of posts.slice(0, 5)) { // Check first 5 posts
        const original = this.originalPosts.find(op => op.slug === post.slug)
        if (original) {
          if (!post.content || post.content.length === 0) {
            throw new Error(`Empty content for post: ${post.slug}`)
          }
          checkedCount++
        }
      }

      return `Checked content integrity for ${checkedCount} posts`
    })
  }

  private async testPerformance(): Promise<void> {
    console.log('⚡ Testing performance...')

    // Test 1: All posts load time
    await this.runTest('All posts load time', async () => {
      const startTime = Date.now()
      await getAllBlogPosts()
      const duration = Date.now() - startTime

      if (duration > 5000) { // 5 seconds threshold
        throw new Error(`Load time too slow: ${duration}ms`)
      }

      return `Loaded in ${duration}ms`
    })
  }

  private async testErrorHandling(): Promise<void> {
    console.log('🛡️ Testing error handling...')

    // Test 1: Non-existent post
    await this.runTest('Non-existent post handling', async () => {
      const post = await getBlogPostBySlug('non-existent-post-12345')
      if (post !== null) {
        throw new Error('Expected null for non-existent post')
      }
      return 'Correctly returned null for non-existent post'
    })
  }

  private async testUrlStructure(): Promise<void> {
    console.log('🔗 Testing URL structure...')

    // Test 1: Slug preservation
    await this.runTest('Slug preservation', async () => {
      const posts = await getAllBlogPosts()
      let preservedCount = 0

      for (const post of posts.slice(0, 5)) {
        const original = this.originalPosts.find(op => op.slug === post.slug)
        if (original) {
          preservedCount++
        }
      }

      if (preservedCount === 0) {
        throw new Error('No slugs preserved from original posts')
      }

      return `${preservedCount} slugs preserved correctly`
    })
  }

  private async testContentTransformation(): Promise<void> {
    console.log('🔄 Testing content transformation...')

    // Test 1: Astro content transformation
    await this.runTest('Astro content transformation', async () => {
      const posts = await getAllBlogPosts()
      const astroContent = transformBlogPostsToAstroContent(posts.slice(0, 3))

      if (astroContent.length === 0) {
        throw new Error('No content transformed')
      }

      // Check structure
      const firstPost = astroContent[0]
      if (!firstPost.slug || !firstPost.data || !firstPost.render) {
        throw new Error('Invalid Astro content structure')
      }

      return `Transformed ${astroContent.length} posts to Astro format`
    })
  }

  private async runTest(name: string, testFn: () => Promise<string>): Promise<void> {
    const startTime = Date.now()

    try {
      const message = await testFn()
      const duration = Date.now() - startTime

      this.results.push({
        name,
        passed: true,
        message,
        duration
      })

      console.log(`  ✅ ${name}: ${message} (${duration}ms)`)
    } catch (error) {
      const duration = Date.now() - startTime
      const message = error instanceof Error ? error.message : String(error)

      this.results.push({
        name,
        passed: false,
        message,
        duration
      })

      console.log(`  ❌ ${name}: ${message} (${duration}ms)`)
    }
  }

  private generateReport(): void {
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.filter(r => !r.passed).length
    const totalDuration = this.results.reduce((sum, r) => sum + (r.duration || 0), 0)

    console.log('\n📊 Test Results Summary:')
    console.log(`✅ Passed: ${passed}`)
    console.log(`❌ Failed: ${failed}`)
    console.log(`⏱️  Total Duration: ${totalDuration}ms`)
    console.log(`📈 Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`)

    if (failed > 0) {
      console.log('\n❌ Failed Tests:')
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.message}`))
    }

    console.log('\n' + (failed === 0 ? '🎉 All tests passed!' : '⚠️  Some tests failed. Please review and fix issues.'))
  }
}

// Main execution
async function main() {
  const tester = new BlogMigrationTester()

  try {
    await tester.runAllTests()
  } catch (error) {
    console.error('💥 Test execution failed:', error)
    process.exit(1)
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}