#!/usr/bin/env node

/**
 * Migration script to transfer existing markdown blog posts to PocketBase content_items collection
 *
 * Usage: npm run migrate-blog
 *
 * This script:
 * 1. Reads all markdown files from src/content/blog/
 * 2. Parses frontmatter and content
 * 3. Creates records in PocketBase content_items collection
 * 4. <PERSON>les duplicate detection and updates
 */

import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import PocketBase from 'pocketbase'

// Configuration
const BLOG_CONTENT_DIR = path.join(process.cwd(), 'src/content/blog')
const POCKETBASE_URL = process.env.POCKETBASE_URL || 'http://localhost:8090'
const ADMIN_EMAIL = process.env.POCKETBASE_ADMIN_EMAIL
const ADMIN_PASSWORD = process.env.POCKETBASE_ADMIN_PASSWORD

// Interface for blog post frontmatter
interface BlogFrontmatter {
  itemId: string
  title: string
  datePublished: string
  dateUpdated?: string
  metaImage: string
  author: string
  metaAuthor: string
  previousPost?: string
  previousPostTitle?: string
  nextPost?: string
  nextPostTitle?: string
  link?: string
  summary: string
}

// Interface for content_items record
interface ContentItemData {
  type: string
  title: string
  slug: string
  summary: string
  body_content: string
  meta_image: string
  author: string
  meta_author: string
  date_published: string
  date_updated?: string
  previous_post?: string
  previous_post_title?: string
  next_post?: string
  next_post_title?: string
  status: string
  published_at: string
}

class BlogMigrator {
  private pb: PocketBase
  private migratedCount = 0
  private skippedCount = 0
  private errorCount = 0

  constructor() {
    this.pb = new PocketBase(POCKETBASE_URL)
  }

  async authenticate(): Promise<void> {
    if (!ADMIN_EMAIL || !ADMIN_PASSWORD) {
      throw new Error('POCKETBASE_ADMIN_EMAIL and POCKETBASE_ADMIN_PASSWORD environment variables are required')
    }

    try {
      await this.pb.admins.authWithPassword(ADMIN_EMAIL, ADMIN_PASSWORD)
      console.log('✅ Authenticated with PocketBase as admin')
    } catch (error) {
      throw new Error(`Failed to authenticate with PocketBase: ${error}`)
    }
  }

  async migrate(): Promise<void> {
    console.log('🚀 Starting blog content migration...')

    // Read all markdown files
    const files = fs.readdirSync(BLOG_CONTENT_DIR).filter(file => file.endsWith('.md'))
    console.log(`📁 Found ${files.length} markdown files to migrate`)

    for (const file of files) {
      await this.migrateFile(file)
    }

    console.log('\n📊 Migration Summary:')
    console.log(`✅ Migrated: ${this.migratedCount}`)
    console.log(`⏭️  Skipped: ${this.skippedCount}`)
    console.log(`❌ Errors: ${this.errorCount}`)
  }

  private async migrateFile(filename: string): Promise<void> {
    try {
      const filePath = path.join(BLOG_CONTENT_DIR, filename)
      const fileContent = fs.readFileSync(filePath, 'utf-8')

      // Parse frontmatter and content
      const { data: frontmatter, content } = matter(fileContent) as {
        data: BlogFrontmatter,
        content: string
      }

      // Generate slug from filename
      const slug = filename.replace('.md', '')

      // Check if record already exists
      const existingRecord = await this.findExistingRecord(slug)

      if (existingRecord) {
        console.log(`⏭️  Skipping ${slug} - already exists`)
        this.skippedCount++
        return
      }

      // Transform data for PocketBase
      const contentItemData: ContentItemData = {
        type: 'blog',
        title: frontmatter.title,
        slug: slug,
        summary: frontmatter.summary,
        body_content: content,
        meta_image: frontmatter.metaImage,
        author: frontmatter.author,
        meta_author: frontmatter.metaAuthor,
        date_published: frontmatter.datePublished,
        date_updated: frontmatter.dateUpdated,
        previous_post: frontmatter.previousPost,
        previous_post_title: frontmatter.previousPostTitle,
        next_post: frontmatter.nextPost,
        next_post_title: frontmatter.nextPostTitle,
        status: 'published',
        published_at: frontmatter.datePublished
      }

      // Create record in PocketBase
      await this.pb.collection('content_items').create(contentItemData)

      console.log(`✅ Migrated: ${slug}`)
      this.migratedCount++

    } catch (error) {
      console.error(`❌ Error migrating ${filename}:`, error)
      this.errorCount++
    }
  }

  private async findExistingRecord(slug: string): Promise<any> {
    try {
      return await this.pb.collection('content_items').getFirstListItem(
        `type = 'blog' && slug = '${slug}'`
      )
    } catch (error) {
      // Record not found
      return null
    }
  }

  async validateEnvironment(): Promise<void> {
    // Check if blog content directory exists
    if (!fs.existsSync(BLOG_CONTENT_DIR)) {
      throw new Error(`Blog content directory not found: ${BLOG_CONTENT_DIR}`)
    }

    // Check if PocketBase is accessible
    try {
      await this.pb.health.check()
      console.log('✅ PocketBase is accessible')
    } catch (error) {
      throw new Error(`Cannot connect to PocketBase at ${POCKETBASE_URL}: ${error}`)
    }
  }
}

// Main execution
async function main() {
  const migrator = new BlogMigrator()

  try {
    console.log('🔍 Validating environment...')
    await migrator.validateEnvironment()

    console.log('🔐 Authenticating...')
    await migrator.authenticate()

    console.log('📝 Starting migration...')
    await migrator.migrate()

    console.log('\n🎉 Migration completed successfully!')
  } catch (error) {
    console.error('\n💥 Migration failed:', error)
    process.exit(1)
  }
}

// Run the migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}